#!/bin/bash

# 超级精简智能修复脚本
# 用法：./fix_sitemap.sh

echo "🚀 智能修复sitemap.xml..."

# 一行检测+修复
cp dist/sitemap.xml dist/sitemap.xml.backup && \
if [[ $(wc -l < dist/sitemap.xml) -le 3 ]]; then
    # 压缩格式
    sed -i '' 's|/src/pages||g; s|<loc>\(https://sline\.dev/[^<][^<]*\)</loc>|<loc>\1.html</loc>|g' dist/sitemap.xml
else
    # 格式化格式
    sed -i '' '/https:\/\//s|/src/pages||g; /https:\/\/[^[:space:]]*\/[^[:space:]\/]*[[:space:]]*$/s|\([^[:space:]]*\)\([[:space:]]*\)$|\1.html\2|' dist/sitemap.xml
fi && \
echo "✅ 修复完成！备份：dist/sitemap.xml.backup" || \
echo "❌ 修复失败！" 