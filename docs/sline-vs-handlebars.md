# Sline & Handlebars 语法差异对比

| 特性分类 | 功能点 | Sline 语法 | Handlebars 语法 | Liquid 语法 | 备注 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| **基础语法** | 标准输出 | `{{ expression }}` | `{{ expression }}` | `{{ expression }}` | 三者通用，默认进行 HTML 转义。 |
| | 原始输出 | `{{{ expression }}}` | `{{{ expression }}}` | `{% raw %}...{% endraw %}` | Sline 继承自 Handlebars。Liquid 使用标签处理代码块。 |
| | 逻辑/标签分隔符 | `{{#tag}}...{{/tag}}` | `{{#helper}}...{{/helper}}` | `{% tag %}...{% endtag %}` | Sline 的核心结构基于 Handlebars。 |
| | 注释 | `{{!-- comment --}}` | `{{!-- comment --}}` | `{% comment %}...{% endcomment %}` | Sline 再次遵循 Handlebars 的语法。 |
| **数据处理** | 变量赋值 | `{{#set 'var' 'val'}}` | 需自定义助手 | `{% assign var = 'val' %}` | Sline 为弥补 Handlebars 的不足而引入了此关键功能。 |
| | 捕获块为变量 | `{{#capture 'var'}}...{{/capture}}` | 需自定义助手 | `{% capture var %}...{% endcapture %}` | Sline 提供了与 Liquid 同等强大的块捕获能力。 |
| | 数组索引访问 | `{{ array }}` | `{{ array. }}` | `{{ array }}` | Sline 采用了更符合直觉和 JS 习惯的语法。 |
| | 访问父级作用域 | 不支持 `../` | `{{../property }}` | 不支持，需通过 `render` 传参 | Sline 为实现更好的封装性而禁用了此功能。 |
| **控制流** | 条件判断 | `{{#if}}...{{else if}}...{{else}}...{{/if}}` | `{{#if}}...{{else if}}...{{else}}...{{/if}}` | `{% if %}...{% elsif %}...{% else %}...{% endif %}` | 语法结构不同，但逻辑能力相似。 |
| | 反向条件 | `{{#unless}}...{{/unless}}` | `{{#unless}}...{{/unless}}` | `{% unless %}...{% endunless %}` | 三者均支持反向条件判断。 |
| | Switch/Case | `{{#switch}}...{{#case}}...{{/switch}}` | 需自定义助手 | `{% case %}...{% when %}...{% endcase %}` | Sline 引入了 Liquid 的 `case` 语句以增强逻辑处理。 |
| **迭代** | For 循环 | `{{#for array as \|item\|}}...{{/for}}` | `{{#each array as \|item\|}}...{{/each}}` | `{% for item in array %}...{% endfor %}` | Sline 的 `for` 助手是 Handlebars 语法和 Liquid 功能的混合体。 |
| | 循环元数据 | `forloop.index`, `forloop.first` | `@index`, `@first` | `forloop.index`, `forloop.first` | Sline 采用了 Liquid 的命名约定，更具可读性。 |
| | 访问父级循环 | `forloop.parentloop` | 不支持 | `forloop.parentloop` | Sline 再次与 Liquid 保持一致，以处理嵌套循环。 |
| | 循环参数 | `limit`, `offset`, `reversed` | 需自定义助手 | `limit`, `offset`, `reversed` | Sline 实现了 Liquid 的强大循环修饰符。 |
| **模块化** | 引入代码片段 | `{{#component 'name'}}` | `{{> partialName }}` | `{% render 'snippet-name' %}` | Sline 倾向于使用更现代的“组件”概念。 |
| | 向片段传递数据 | `{{#component 'name' key=val}}` | `{{> partialName context}}` | `{% render 'name', key: val %}` | 语法各异，但都支持显式数据传递。 |
| **数据转换** | 过滤器/助手语法 | `{{ value \| filter:arg }}` | `{{ helper value arg }}` | `{{ value \| filter: arg }}` | Sline 采纳了 Liquid 的管道语法，是其关键设计决策。 |
| | 链式调用 | `{{ val \| f1 \| f2 }}` | `{{ f2 (f1 val) }}` | `{{ val \| f1 \| f2 }}` | 管道语法的可读性远优于嵌套函数调用。 |