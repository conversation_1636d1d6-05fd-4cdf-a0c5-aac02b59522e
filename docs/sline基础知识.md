# 基础知识

通过以下基础概念，你可以快速了解如何使用 Sline [objects](https://developer.shopline.com/zh-hans-cn/docs/sline/object/additional-checkout-buttons?version=v20250601)，[filters](https://developer.shopline.com/zh-hans-cn/docs/sline/filter/abs?version=v20250601)，和 [tags](https://developer.shopline.com/zh-hans-cn/docs/sline/tag/activate-customer-password-form?version=v20250601) 进行主题开发。

## 资源 handles

资源 handles 是识别不同店铺资源的标识符，用于构建资源的 URL 或者获取资源对象。

拥有 handle 的常见资源对象有 [product](https://developer.shopline.com/zh-hans-cn/docs/sline/object/product?version=v20250601)、[collection](https://developer.shopline.com/zh-hans-cn/docs/sline/object/collection?version=v20250601)、[article](https://developer.shopline.com/zh-hans-cn/docs/sline/object/article?version=v20250601) 和 [blog](https://developer.shopline.com/zh-hans-cn/docs/sline/object/blog?version=v20250601)。其它类型的对象资源，如 [linklists](https://developer.shopline.com/zh-hans-cn/docs/sline/object/linklists?version=v20250601) 和 [link](https://developer.shopline.com/zh-hans-cn/docs/sline/object/link?version=v20250601)，也同样具有 handles。

同一对象的不同资源均拥有唯一的 handle 标识。以 [product](https://developer.shopline.com/zh-hans-cn/docs/sline/object/product?version=v20250601) 对象为例，如果一个店铺有两个不同的产品，两个产品资源的 handle 是不同的。

### 创建和修改 handle

资源创建的时候，资源标题会填充到 handle 上，并遵循一组规则：

- handle 始终为小写，输入大写会被转换为小写。
- 空格和特殊字符被替换为连字符 `-`。
- 如果有多个连续的空格或特殊字符，则它们被替换为单个连字符。
- 开头的空格或特殊字符将被移除。
- 同一个资源下的 handle 需要保持唯一性，因此如果使用了重复的标题，则 handle 会自动递增一。例如，如果有两个名为 "bed" 的产品，则它们的 handle 分别为 bed 和 bed-1。

创建资源后，更改资源标题不会更新 handle。

你可以在 SHOPLINE 商家后台中修改资源的 handle。在具体资源的管理页面的 **搜索引擎优化** 组件编辑 handle 信息。如果通过 handle 引用资源，确保在修改 handle 时更新这些引用。

注意：
- linklists 中的个别链接根据其标题生成 handle（例如 linklists.header），这些 handle 无法直接修改。
- theme.config.json、sections 或 blocks 中获取的个别设置使用其 id 属性作为 handle。

### 使用 handle

所有具有 handle 的对象都有一个 `handle` 属性。例如，你可以使用 `product.handle` 输出产品的 handle。你可以通过两种方式在父对象内引用一个对象，这个对象是通过其 handle 标识的：

- **方括号表示法** `[ ]`：接受方括号内的 handle 字符串或变量，例如 `linklists["header"]` 或 `linklists[header]`。
- **点表示法** `.`：接受不带方括号的 handle，例如 `linklists.header`。

## 运算符

Sline 支持基本的逻辑和比较运算符，可用于 [if](https://developer.shopline.com/zh-hans-cn/docs/sline/tag/if?version=v20251201) tag 和取值表达式。

| **运算符** | **功能** |
|----------|----------|
| `==` | 等于 |
| `!=` | 不等于 |
| `>` | 大于 |
| `<` | 小于 |
| `>=` | 大于等于 |
| `<=` | 小于等于 |
| `\|\|` | 条件 A 或条件 B |
| `&&` | 条件 A 和条件 B |

### 运算符执行顺序

在 Sline 模板中，逻辑运算符的优先级遵循常规编程语言的规则：`&&` 的优先级高于 `\|\|`。条件表达式会从左到右计算，但会先计算所有 `&&` 运算，然后再计算 `\|\|` 运算。

```sline
{{#if false || true || true && false }}
This evaluates to true, because the calculation proceeds as follows: 1. false || true → true 
2. true && false → false 
3. true || false → true
{{/if}}
```

如果你想在计算中提高优先级，就使用 `()`。

```sline
{{#if true && (true && true) || false }}
This evaluates to true, with explicit calculation steps: 
1. (true && true) → true 
2. true && true → true 
3. true || false → true
{{/if}}
```

## 类型

Sline 支持六种数据类型。

### 字符串（string）

任何一系列的字符，可以用双引号 `""`、单引号 `''` 或反引号 ` `` ` 括起来。

备注：可以使用 `""` 来检查字符串是否为空。

#### 双引号 `""`
标准字符串，支持换行字符（如 "\n"）。

#### 单引号 `''`
表示单个字符。

#### 反引号 `` ` ` ``
原始字符串，不支持转译，可跨多行。

### 数字（number）

包含整型（`int`）和浮点型（`float`），直接书写数值即可。

```sline
{{ 42 }}   <!-- 整数 -->
{{ 3.14 }} <!-- 浮点数 -->
```

### 布尔（boolean）

二进制值，要么是 `true`，要么是 `false`。

### 空值（nil）

未定义的值，用 `nil` 表示。返回 `nil` 的表达式不会输出任何内容，且在条件判断中视为 `false`。

### 数组（array）

任何类型的变量列表。

要访问数组中的所有项，可以使用 [for](https://developer.shopline.com/zh-hans-cn/docs/sline/tag/for?version=v20250601) tag 遍历数组中的每一项。

可以使用方括号 `[ ]` 表示法访问数组中的特定项。数组索引从零开始。

在 Sline 中无法仅使用其本身初始化数组，不过，你可以使用 [split](https://developer.shopline.com/zh-hans-cn/docs/sline/filter/split) filter 将单个字符串拆分为子字符串数组。

备注：可以使用 [size](https://developer.shopline.com/zh-hans-cn/docs/sline/filter/size?version=v20251201) filter 来获取数组长度，判断数组长度是否大于 0。

## 真值和假值

所有数据类型必须返回 `true` 或 `false`。默认返回 `true` 的被称为真值（truthy），默认返回 `false` 的被称为假值（falsy）。

| **值** | **真值** | **假值** |
|-------|----------|----------|
| true | ✔️ | |
| false | | ✔️ |
| nil | | ✔️ |
| 字符串 | ✔️ | |
| 空字符串 |✔️| |
| 整数 | ✔️ | |
| 浮点数 | ✔️ | |
| 数组 | ✔️ | |
| 空数组 |✔️| |

### 例子

在 Sline 中检查值时需要小心。一个值可能不是你预期的格式，但仍然可能是真值。

```sline
<!-- True if current_tags is an empty array. Use size to determine if it is empty. -->
{{#if current_tags | size() > 0}}
Presence tag.
{{#else/}}
Tag is empty.
{{/if}}
```

## 空白符控制

可以通过在花括号旁边添加一个 `~` 字符来省略模板中任何 Sline 语句的两侧空格、换行符，制表符等空白符。应用后，该侧的所有空白符将被删除，直到第一个 Sline 表达式或该侧的非空白符为止。

```sline
start{{~ "content" ~}}end
```