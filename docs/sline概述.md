# Sline 概述

Sline 是一种模板语言，允许你创建一个模板来托管静态内容，并根据模板的渲染位置动态插入信息。例如，你可以创建一个产品模板，其中包含所有标准的产品属性，如产品图片、标题和价格。然后，该模板可以根据当前正在查看的产品动态地呈现这些属性，显示相应的内容。

## Sline 的使用

Sline 被用于动态呈现对象及其属性。你可以通过使用 [tag][tag-link] 编写逻辑，使用 [filter][filter-link] 修改输出。[object][object-link] 及其属性使用Sline 表达式进行呈现，格式为双花括号，如 `{{ 表达式 }}` 。此外，三花括号 `{{{ 表达式 }}}` 用于直接输出未经 HTML 转义的内容，使其中的 HTML 标签作为纯文本显示。

注意：

Sline 的 tag/filter 完全内部实现，不支持自定义注册。

```html
<!-- Render html title -->
<title>{{ page_title }}</title>
<!-- Render html meta description -->
{{#if page_description}}
<meta name="description" content="{{ page_description | truncate(150) }}">
{{/if}}
<!-- Render unescaped html -->
<div class="product_description_container">
  {{{ product.description }}}
</div>
```

## Sline 资源

### 基础知识

Sline 基础知识主要包含了资源 handle 的创建与使用、逻辑运算符和比较运算符的运用、以及对象及其属性输出的数据类型。在开始使用 Sline 之前，参考 [Sline 基础知识][basics-link]  了解该部分的内容。

### Tags

Sline 标签用于定义指示模板执行操作的逻辑。标签分隔符内的文本在网页渲染时不会产生可见的输出。

提示

标签分为普通标签和自闭合标签

- 普通标签是以 `{{# tag_name }}` 开头 `{{/ tag_name }}` 结尾。
- 自闭合标签 `{{# tag_name /}}`。

```html
{{#if customer != nil}}
  User {{customer.name}} is logged in!
{{/if}}
{{#product_form /}}
```

### Filter

Sline filter 用于修改变量和对象的输出。可在`{{}}`中或者在 tag 中添加管道符后使用。

一个输出可以使用多个 filter。它们按从左到右的顺序进行解析。

```html
<div class="product-page">
  <div class="product-image">
    <!-- get the product image link to generate the image tag -->
    {{#image_tag product.featured_image | image_url() /}}
  </div>
  <div class="product-title">
    {{ product.title }}
  </div>
  <div class="product-price">
    {{ product.price | money() }}
  </div>
</div>
```

### Object

Sline 对象代表你可以用来构建主题的变量。对象类型包括但不限于：

- 店铺资源，如 [collection][collection-link] 或 [product][product-link] 及其属性。
- 用于构建互动性的功能元素，如 [paginate][paginate-link] 和 `search`。

对象既可以代表单一的数据点，比如 [page_title][page-title-link]，也可以是一个包含多种属性的结构，比如 [product][product-link]。

#### 作用域

对象分为全局和局部两种。全局对象可在所有 [主题模板][theme-link] 中使用，例如 [page_title][page-title-link]。而由 tag 定义的局部对象仅限在定义它的当前模板文件中使用，例如 [forloop][forloop-link]。

[tag-link]: https://developer.shopline.com/zh-hans-cn/docs/sline/sline-overview?version=v20251201#LFv2q
[filter-link]: https://developer.shopline.com/zh-hans-cn/docs/sline/sline-overview?version=v20251201#L5H40
[object-link]: https://developer.shopline.com/zh-hans-cn/docs/sline/sline-overview?version=v20251201#lvfYC
[basics-link]: https://developer.shopline.com/zh-hans-cn/docs/sline/basics?lang=zh-hans-cn
[collection-link]: https://developer.shopline.com/zh-hans-cn/docs/sline/object/collection?version=v20251201
[product-link]: https://developer.shopline.com/zh-hans-cn/docs/sline/object/product?version=v20251201
[paginate-link]: https://developer.shopline.com/zh-hans-cn/docs/sline/object/paginate
[page-title-link]: https://developer.shopline.com/zh-hans-cn/docs/sline/object/page-title?version=v20251201
[theme-link]: https://developer.shopline.com/zh-hans-cn/docs/online-store-3-0-themes/theme-structure/templates?version=v20250601
[forloop-link]: https://developer.shopline.com/zh-hans-cn/docs/sline/object/forloop?version=v20251201
