import { resolve } from 'path'
import packageJson from './package.json'
import { defineConfig } from "vite"
import vanilla from 'vite-plugin-vanilla'
import Sitemap from 'vite-plugin-sitemap'

export default defineConfig({
    resolve: {
        alias: {
            '@': resolve(__dirname, 'src'),
            '@js': resolve(__dirname, 'src/js'),
            '@css': resolve(__dirname, 'src/scss'),
            '@json': resolve(__dirname, 'src/json'),
        },
        extensions: ['.js', '.ts', '.json', '.scss', '.css'],
    },
    plugins: [
        vanilla({
            include: 'src/pages/**/*.html',
            base: 'src/pages',
        }),
        Sitemap({
            hostname: packageJson.domain,
            readable: true,
        }),
    ],
    build: {
        assetsInlineLimit: 4096,
    },
    server: {
        host: true
    },
})