
import './index.scss';
import $ from 'jquery';

// i18n 多语言数据
const i18nMessages = {
    'zh-CN': {
        // 导航栏
        'nav-features': '特性',
        'nav-syntax': '语法对比',
        'nav-stats': '核心优势',
        'nav-get-started': '🚀 开始使用',
        
        // 英雄区域
        'hero-title': 'Sline 模板引擎',
        'hero-subtitle': 'SHOPLINE 自主研发的电商模板引擎',
        'hero-description': 'SHOPLINE 自主研发的高性能模板渲染引擎，为 Online Store 3.0 主题构建而成。提供轻量级设计、高效渲染能力，专为电商场景优化。支持主题事件系统和多层级区块嵌套，助力主题个性化定制，零第三方依赖确保最佳性能。',
        
        // 特性区域  
        'features-title': '核心特性',
        'features-subtitle': '探索 Sline 模板引擎为 SHOPLINE Online Store 3.0 主题带来的强大特性',
        'feature-performance-title': '轻量级高性能',
        'feature-performance-desc': '轻量级设计有效减少加载耗时，高性能运转保障页面流畅呈现。专为电商场景优化，确保您的店铺快速响应。',
        'feature-ecommerce-title': '电商场景专用',
        'feature-ecommerce-desc': '专为 SHOPLINE Online Store 3.0 主题设计，深度集成电商功能。针对商品展示、购物体验进行专项优化。',
        'feature-events-title': '主题事件系统',
        'feature-events-desc': '内置标准主题事件系统，支持商品浏览、SKU切换、加购等事件监听。实现更丰富的交互功能，提升用户体验。',
        'feature-nesting-title': '多层级区块嵌套',
        'feature-nesting-desc': '支持多层级区块嵌套设计，灵活控制页面布局和功能。实现复杂页面效果，让商家自定义搭建得心应手。',
        'feature-dependencies-title': '零第三方依赖',
        'feature-dependencies-desc': '零第三方包引入，减少页面加载负担。使用 CSS3 动画代替 JavaScript，配合图片懒加载，提升页面流畅度。',
        'feature-architecture-title': '易扩展架构',
        'feature-architecture-desc': '易扩展架构设计，助力主题个性化定制。为所有 SHOPLINE Online Store 3.0 主题提供统一、可靠的基础。',
        
        // 语法对比
        'syntax-title': '语法对比',
        'syntax-subtitle': 'Sline 与 Liquid 模板语法的差异对比，了解 SHOPLINE 专有引擎的特色',
        'syntax-liquid': 'Liquid 语法',
        'syntax-sline': 'Sline 语法',
        
        // 统计数据
        'stats-title': '核心优势',
        'stats-subtitle': '基于 SHOPLINE 官方文档的 Sline 模板引擎核心优势',
        'stat-lightweight': '轻量级',
        'stat-lightweight-desc': '设计减少加载耗时',
        'stat-zerodep': '零依赖',
        'stat-zerodep-desc': '无第三方包引入',
        'stat-professional': '专业级',
        'stat-professional-desc': '电商场景深度优化',
        'stat-enterprise': '企业级',
        'stat-enterprise-desc': 'SHOPLINE 官方支持',
        
        // CTA 区域
        'cta-title': '了解更多',
        'cta-subtitle': '探索 Sline 模板引擎在 SHOPLINE Online Store 3.0 主题中的应用。查看官方对比报告，了解与其他模板引擎的差异，或学习如何使用 SHOPLINE CLI 进行主题开发。',
        'cta-comparison': '查看 Dawn vs Bottle 对比报告',
        'cta-cli': 'SHOPLINE CLI 开发指南',
        
        // 页脚
        'footer-product': '产品',
        'footer-product-features': '核心特性',
        'footer-product-syntax': '语法文档',
        'footer-product-start': '快速开始',
        'footer-tools': '开发工具',
        'footer-tools-vscode': 'Sline 语法高亮扩展',
        'footer-tools-mcp': 'Sline MCP 服务器',
        'footer-tools-converter': 'Handlebars to Sline 语法转换器',
        'footer-tools-liquid-to-sline-converter': 'Liquid to Sline 语法转换器',
        'footer-tools-ast': 'Sline AST Explorer',
        'footer-tools-analyzer': '类型分析器',
        'footer-community': '社区',
        'footer-community-github': 'GitHub',
        'footer-community-issues': '问题反馈',
        'footer-community-contribute': '贡献指南',
        'footer-support': '支持',
        'footer-support-docs': '使用文档',
        'footer-support-api': 'API 参考',
        'footer-support-contact': '联系我们',
        'footer-copyright': '&copy; 2025 <a href="https://sline.dev">Sline 模板引擎</a>.',
        'footer-copyright-link': '免责声明'
    },
    
    'zh-TW': {
        // 導航欄
        'nav-features': '產品特色',
        'nav-syntax': '語法比較',
        'nav-stats': '核心優勢',
        'nav-get-started': '🚀 立即開始',
        
        // 英雄區域
        'hero-title': 'Sline 模板引擎',
        'hero-subtitle': 'SHOPLINE 獨家開發的電商模板引擎',
        'hero-description': 'SHOPLINE 獨家開發的高效能模板渲染引擎，專為 Online Store 3.0 主題打造。採用輕量化設計、提供高效渲染效能，針對電商應用場景最佳化。內建主題事件系統及多層級區塊巢狀功能，協助主題客製化開發，無第三方套件相依性，確保最佳效能表現。',
        
        // 特性區域
        'features-title': '產品特色',
        'features-subtitle': '探索 Sline 模板引擎為 SHOPLINE Online Store 3.0 主題帶來的強大功能',
        'feature-performance-title': '輕量高效能',
        'feature-performance-desc': '輕量化設計有效縮短載入時間，高效能運作確保頁面順暢呈現。針對電商應用場景最佳化，讓您的商店快速回應顧客需求。',
        'feature-ecommerce-title': '電商專用設計',
        'feature-ecommerce-desc': '專為 SHOPLINE Online Store 3.0 主題設計，深度整合電商功能。針對商品展示、購物流程進行專業最佳化。',
        'feature-events-title': '主題事件系統',
        'feature-events-desc': '內建標準主題事件系統，支援商品瀏覽、規格切換、加入購物車等事件監聽。實現更豐富的互動功能，提升使用者體驗。',
        'feature-nesting-title': '多層級區塊巢狀',
        'feature-nesting-desc': '支援多層級區塊巢狀設計，彈性控制頁面版面配置及功能。實現複雜頁面效果，讓商家客製化建置更加容易。',
        'feature-dependencies-title': '零相依性設計',
        'feature-dependencies-desc': '不引入第三方套件，減輕頁面載入負擔。採用 CSS3 動畫取代 JavaScript，搭配圖片延遲載入，提升頁面流暢度。',
        'feature-architecture-title': '易於擴充架構',
        'feature-architecture-desc': '採用易於擴充的架構設計，協助主題客製化開發。為所有 SHOPLINE Online Store 3.0 主題提供統一且可靠的基礎架構。',
        
        // 語法對比
        'syntax-title': '語法比較',
        'syntax-subtitle': 'Sline 與 Liquid 模板語法差異比較，認識 SHOPLINE 獨家引擎特色',
        'syntax-liquid': 'Liquid 語法',
        'syntax-sline': 'Sline 語法',
        
        // 統計數據
        'stats-title': '核心優勢',
        'stats-subtitle': '基於 SHOPLINE 官方文件的 Sline 模板引擎核心優勢',
        'stat-lightweight': '輕量化',
        'stat-lightweight-desc': '設計減少載入時間',
        'stat-zerodep': '零相依性',
        'stat-zerodep-desc': '無第三方套件引入',
        'stat-professional': '專業級',
        'stat-professional-desc': '電商場景深度最佳化',
        'stat-enterprise': '企業級',
        'stat-enterprise-desc': 'SHOPLINE 官方技術支援',
        
        // CTA 區域
        'cta-title': '了解更多',
        'cta-subtitle': '深入了解 Sline 模板引擎在 SHOPLINE Online Store 3.0 主題中的應用。查看官方比較報告，了解與其他模板引擎的差異，或學習如何使用 SHOPLINE CLI 進行主題開發。',
        'cta-comparison': '查看 Dawn vs Bottle 比較報告',
        'cta-cli': 'SHOPLINE CLI 開發指南',
        
        // 頁腳
        'footer-product': '產品',
        'footer-product-features': '產品特色',
        'footer-product-syntax': '語法文件',
        'footer-product-start': '快速上手',
        'footer-tools': '開發工具',
        'footer-tools-vscode': 'Sline 語法高亮擴充功能',
        'footer-tools-mcp': 'Sline MCP 伺服器',
        'footer-tools-converter': 'Handlebars to Sline 語法轉換器',
        'footer-tools-liquid-to-sline-converter': 'Liquid to Sline 語法轉換器',
        'footer-tools-ast': 'Sline AST Explorer',
        'footer-tools-analyzer': '型別分析器',
        'footer-community': '社群',
        'footer-community-github': 'GitHub',
        'footer-community-issues': '問題回報',
        'footer-community-contribute': '貢獻指南',
        'footer-support': '技術支援',
        'footer-support-docs': '使用文件',
        'footer-support-api': 'API 參考文件',
        'footer-support-contact': '聯絡我們',
        'footer-copyright': '&copy; 2025 Sline 模板引擎。',
        'footer-copyright-link': '免責聲明'
    },
    
    'en': {
        // Navigation
        'nav-features': 'Features',
        'nav-syntax': 'Syntax',
        'nav-stats': 'Why Choose Sline',
        'nav-get-started': '🚀 Get Started',
        
        // Hero Section
        'hero-title': 'Sline Template Engine',
        'hero-subtitle': 'SHOPLINE\'s Custom-Built E-commerce Template Engine',
        'hero-description': 'SHOPLINE\'s custom-built, high-performance template rendering engine designed specifically for Online Store 3.0 themes. Features a lightweight architecture with lightning-fast rendering, purpose-built for e-commerce needs. Includes a powerful theme event system and multi-level block nesting to make theme customization seamless, all while maintaining zero dependencies for peak performance.',
        
        // Features Section
        'features-title': 'Key Features',
        'features-subtitle': 'Discover the powerful capabilities that make Sline the perfect engine for SHOPLINE Online Store 3.0 themes',
        'feature-performance-title': 'Lightning-Fast Performance',
        'feature-performance-desc': 'Built from the ground up for speed. Our lightweight architecture dramatically reduces load times while maintaining silky-smooth page rendering. Engineered specifically for e-commerce, ensuring your store delivers instant responses to every customer interaction.',
        'feature-ecommerce-title': 'E-commerce First Design',
        'feature-ecommerce-desc': 'Purpose-built for SHOPLINE Online Store 3.0 themes with deep e-commerce integration baked in. Every feature is optimized for product showcases, seamless checkout flows, and converting browsers into buyers.',
        'feature-events-title': 'Smart Event System',
        'feature-events-desc': 'Advanced theme event system that automatically handles product browsing, variant selection, cart interactions, and more. Create rich, interactive shopping experiences that feel natural and responsive.',
        'feature-nesting-title': 'Flexible Block Architecture',
        'feature-nesting-desc': 'Powerful multi-level block nesting gives you complete control over page layouts and functionality. Build complex, beautiful storefronts with ease – no developer expertise required.',
        'feature-dependencies-title': 'Zero Dependencies',
        'feature-dependencies-desc': 'Completely self-contained with no external libraries to slow things down. Pure CSS3 animations replace heavy JavaScript, while smart image loading keeps everything running smoothly.',
        'feature-architecture-title': 'Built to Scale',
        'feature-architecture-desc': 'Extensible architecture that grows with your needs. Provides a rock-solid foundation that powers every SHOPLINE Online Store 3.0 theme with consistency and reliability.',
        
        // Syntax Comparison
        'syntax-title': 'Syntax Comparison',
        'syntax-subtitle': 'See how Sline\'s intuitive syntax compares to Liquid and discover what makes SHOPLINE\'s engine special',
        'syntax-liquid': 'Liquid Syntax',
        'syntax-sline': 'Sline Syntax',
        
        // Statistics
        'stats-title': 'Why Choose Sline',
        'stats-subtitle': 'The numbers speak for themselves – here\'s what makes Sline the smart choice for your store',
        'stat-lightweight': 'Ultra-Light',
        'stat-lightweight-desc': 'Blazing fast load times',
        'stat-zerodep': 'Zero Dependencies',
        'stat-zerodep-desc': 'No bloated third-party code',
        'stat-professional': 'E-commerce Ready',
        'stat-professional-desc': 'Built for online stores',
        'stat-enterprise': 'Enterprise Grade',
        'stat-enterprise-desc': 'Backed by SHOPLINE',
        
        // CTA Section
        'cta-title': 'Ready to Get Started?',
        'cta-subtitle': 'Dive deeper into how Sline powers SHOPLINE Online Store 3.0 themes. Check out our detailed comparison with other template engines, or learn how to build amazing themes with SHOPLINE CLI.',
        'cta-comparison': 'Dawn vs Bottle: Full Comparison',
        'cta-cli': 'SHOPLINE CLI: Developer Guide',
        
        // Footer
        'footer-product': 'Product',
        'footer-product-features': 'Features',
        'footer-product-syntax': 'Syntax Guide',
        'footer-product-start': 'Quick Start',
        'footer-tools': 'Developer Tools',
        'footer-tools-vscode': 'Sline Languages Syntax Highlighter',
        'footer-tools-mcp': 'Sline MCP Server',
        'footer-tools-converter': 'Handlebars to Sline Converter',
        'footer-tools-liquid-to-sline-converter': 'Liquid to Sline Converter',
        'footer-tools-ast': 'Sline AST Explorer',
        'footer-tools-analyzer': 'Type Analyzer',
        'footer-community': 'Community',
        'footer-community-github': 'GitHub',
        'footer-community-issues': 'Report Issues',
        'footer-community-contribute': 'Contributing',
        'footer-support': 'Support',
        'footer-support-docs': 'Documentation',
        'footer-support-api': 'API Reference',
        'footer-support-contact': 'Contact Us',
        'footer-copyright': '&copy; 2025 Sline Template Engine.',
        'footer-copyright-link': 'Disclaimer'
    }
};

// 当前语言
let currentLang = 'en';

// 简单的国际化函数
function i18n(key) {
    return i18nMessages[currentLang] && i18nMessages[currentLang][key] || key;
}

// 初始化国际化
function initI18n() {
    // 设置默认语言（从本地存储获取或使用默认值）
    const savedLang = localStorage.getItem('preferred-language') || 'en';
    switchLanguage(savedLang);
}

// 语言切换功能
function switchLanguage(lang) {
    currentLang = lang;
    
    // 更新页面语言属性
    document.documentElement.lang = lang;
    
    // 更新当前语言显示
    const langMap = {
        'zh-CN': '简',
        'zh-TW': '繁', 
        'en': 'EN'
    };
    $('.current-lang').text(langMap[lang]);
    
    // 更新语言选项的激活状态
    $('.lang-option').removeClass('active');
    $(`.lang-option[data-lang="${lang}"]`).addClass('active');
    
    // 手动更新所有翻译元素
    $('[data-i18n]').each(function() {
        const key = $(this).data('i18n');
        const translation = i18n(key);
        if (translation && translation !== key) {
            $(this).html(translation);
        }
    });
    
    // 保存语言偏好
    localStorage.setItem('preferred-language', lang);
}

// 滚动指示器
$(window).on('scroll', function() {
    const scrollTop = $(window).scrollTop();
    const docHeight = $(document).height() - $(window).height();
    const scrollPercent = scrollTop / docHeight * 100;
    $('#scrollIndicator').css('width', scrollPercent + '%');
});

// 平滑滚动
$(document).on('click', 'a[href^="#"]', function(e) {
    e.preventDefault();
    const target = $(this.getAttribute('href'));
    if (target.length) {
        $('html, body').animate({
            scrollTop: target.offset().top
        }, 800, 'swing');
    }
});

// 导航栏滚动效果
$(window).on('scroll', function() {
    const $navbar = $('#navbar');
    if ($(window).scrollTop() > 100) {
        $navbar.css({
            'background': 'rgba(255, 255, 255, 0.98)',
            'box-shadow': '0 4px 20px rgba(0, 0, 0, 0.1)'
        });
    } else {
        $navbar.css({
            'background': 'rgba(255, 255, 255, 0.95)',
            'box-shadow': 'none'
        });
    }
});

// 元素进入视口动画
const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            $(entry.target).addClass('fade-in-up');
        }
    });
}, observerOptions);

// 观察需要动画的元素
$('.feature-card, .stat-item').each(function() {
    observer.observe(this);
});

// 代码高亮管理器 - 使用 Shiki 3.x 简化版本
// Shiki 3.x 主要改进：
// 1. 使用简洁的 codeToHtml 短语法，无需预创建 highlighter 实例
// 2. 按需加载主题和语言，减少初始化开销
// 3. 更好的 CDN 支持和更小的 bundle 大小
// 4. 自动处理 ESM 模块加载和异步操作
class CodeHighlightManager {
    constructor() {
        this.isInitialized = false;
    }

    // 初始化代码高亮
    async init() {
        if (this.isInitialized) return;
        
        try {
            // 动态导入 Shiki 3.x
            const { codeToHtml } = await import('https://esm.sh/shiki@3.7.0');
            this.codeToHtml = codeToHtml;
            this.isInitialized = true;
            await this.highlightAllCodeBlocks();
        } catch (error) {
            console.error('Shiki 初始化失败:', error);
            this.fallbackToPlainText();
        }
    }

    // 高亮所有代码块
    async highlightAllCodeBlocks() {
        if (!this.codeToHtml) return;
        
        const $codeBlocks = $('pre.shiki code');
        
        for (let i = 0; i < $codeBlocks.length; i++) {
            const codeElement = $codeBlocks[i];
            const $codeElement = $(codeElement);
            const $preElement = $codeElement.parent();
            const code = $codeElement.text();
            
            // 根据内容判断语言
            let lang = 'text';
            if (code.includes('{% for') || code.includes('{{ item }}')) {
                lang = 'liquid';
            } else if (code.includes('{{#for') || code.includes('{{/for}}')) {
                lang = 'handlebars'; // Sline 语法更类似 Handlebars
            }
            
            try {
                // 使用 Shiki 3.x 简化的 codeToHtml API
                const html = await this.codeToHtml(code, {
                    lang: lang,
                    theme: 'github-dark'
                });
                
                // 创建新的 pre 元素
                const $newPreElement = $(html);
                
                // 保持原有的 ID
                if ($preElement.attr('id')) {
                    $newPreElement.attr('id', $preElement.attr('id'));
                }
                
                // 替换元素
                $preElement.replaceWith($newPreElement);
            } catch (error) {
                console.error('代码高亮失败:', error);
            }
        }
    }

    // 降级到纯文本显示
    fallbackToPlainText() {
        $('pre.shiki').css({
            'background-color': '#0d1117',
            'color': '#e6edf3',
            'padding': '1.5rem',
            'border-radius': '6px'
        });
    }
}

// 全局代码高亮管理器实例
const codeManager = new CodeHighlightManager();


// 初始化所有功能
$(function() {

    // 初始化代码高亮管理器
    codeManager.init();

    // 初始化国际化
    initI18n();
    
    // 使用 jQuery 简化语言切换 UI 交互
    $('#langBtn').on('click', function(e) {
        e.stopPropagation();
        $('#langDropdown').toggleClass('show');
    });
    
    // 点击页面其他地方关闭下拉菜单
    $(document).on('click', function() {
        $('#langDropdown').removeClass('show');
    });
    
    // 阻止下拉菜单内部点击事件冒泡，并处理语言选择
    $('#langDropdown').on('click', function(e) {
        e.stopPropagation();
    }).on('click', '.lang-option', function() {
        const lang = $(this).data('lang');
        switchLanguage(lang);
        $('#langDropdown').removeClass('show');
    });
});