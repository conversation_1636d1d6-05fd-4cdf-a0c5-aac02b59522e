<!doctype html>
<html class="no-js" lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="theme-color" content="">
    <link rel="canonical" href="https://section.store/">
    <link rel="preconnect" href="https://cdn.shopify.com" crossorigin>
    <link rel="icon" type="image/png"
        href="//section.store/cdn/shop/files/Frame_2_1.png?crop=center&height=32&v=1663754297&width=32">
    <link rel="preconnect" href="https://fonts.shopifycdn.com" crossorigin>
    <title>
        500+ Shopify Theme Sections
        &ndash; Section Store</title>


    <meta name="description"
        content="Extend the functionality of your current theme | Browse our library of 500+ sections and add to any theme | Edit in theme editor | Install the app now, for FREE.">




    <meta property="og:site_name" content="Section Store">
    <meta property="og:url" content="https://section.store/">
    <meta property="og:title" content="500+ Shopify Theme Sections">
    <meta property="og:type" content="website">
    <meta property="og:description"
        content="Extend the functionality of your current theme | Browse our library of 500+ sections and add to any theme | Edit in theme editor | Install the app now, for FREE.">
    <meta property="og:image" content="http://section.store/cdn/shop/files/g-s1-4_4.png?v=1727175701">
    <meta property="og:image:secure_url" content="https://section.store/cdn/shop/files/g-s1-4_4.png?v=1727175701">
    <meta property="og:image:width" content="1600">
    <meta property="og:image:height" content="900">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="500+ Shopify Theme Sections">
    <meta name="twitter:description"
        content="Extend the functionality of your current theme | Browse our library of 500+ sections and add to any theme | Edit in theme editor | Install the app now, for FREE.">


    <script src="//section.store/cdn/shop/t/1/assets/global.js?v=149496944046504657681663702885" defer="defer"></script>
    <script>window.performance && window.performance.mark && window.performance.mark('shopify.content_for_header.start');</script>
    <meta id="shopify-digital-wallet" name="shopify-digital-wallet" content="/***********/digital_wallets/dialog">
    <script async="async" src="/checkouts/internal/preloads.js?locale=en-US"></script>
    <script id="shopify-features"
        type="application/json">{"accessToken":"7577fbd39c0227c90b61446e0c1e7cfa","betas":["rich-media-storefront-analytics"],"domain":"section.store","predictiveSearch":true,"shopId":***********,"locale":"en"}</script>
    <script>var Shopify = Shopify || {};
        Shopify.shop = "section-store-app.myshopify.com";
        Shopify.locale = "en";
        Shopify.currency = { "active": "USD", "rate": "1.0" };
        Shopify.country = "US";
        Shopify.theme = { "name": "Dawn", "id": 136403255545, "schema_name": "Dawn", "schema_version": "7.0.0", "theme_store_id": 887, "role": "main" };
        Shopify.theme.handle = "null";
        Shopify.theme.style = { "id": null, "handle": null };
        Shopify.cdnHost = "section.store/cdn";
        Shopify.routes = Shopify.routes || {};
        Shopify.routes.root = "/";</script>
    <script type="module">!function (o) { (o.Shopify = o.Shopify || {}).modules = !0 }(window);</script>
    <script>!function (o) { function n() { var o = []; function n() { o.push(Array.prototype.slice.apply(arguments)) } return n.q = o, n } var t = o.Shopify = o.Shopify || {}; t.loadFeatures = n(), t.autoloadFeatures = n() }(window);</script>
    <script id="shop-js-analytics" type="application/json">{"pageType":"index"}</script>
    <script defer="defer" async="async" src="//section.store/cdn/shopifycloud/shop-js/client.js" onload="window.Shopify.SignInWithShop?.initShopCartSync?.({&quot;fedCMEnabled&quot;:true,&quot;windoidEnabled&quot;:true});
"></script>
    <script
        id="__st">var __st = { "a": ***********, "offset": 10800, "reqid": "52f416b8-b12a-446a-8eb6-5e03e455f022-1753452112", "pageurl": "section.store\/", "u": "42248a245d34", "p": "home" };</script>
    <script>window.ShopifyPaypalV4VisibilityTracking = true;</script>
    <script
        id="form-persister">!function () { 'use strict'; const t = 'contact', e = 'new_comment', n = [[t, t], ['blogs', e], ['comments', e], [t, 'customer']], o = 'password', r = 'form_key', c = ['recaptcha-v3-token', 'g-recaptcha-response', 'h-captcha-response', o], s = () => { try { return window.sessionStorage } catch { return } }, i = '__shopify_v', u = t => t.elements[r], a = function () { const t = [...n].map((([t, e]) => `form[action*='/${t}']:not([data-nocaptcha='true']) input[name='form_type'][value='${e}']`)).join(','); var e; return e = t, () => e ? [...document.querySelectorAll(e)].map((t => t.form)) : [] }(); function m(t) { const e = u(t); a().includes(t) && (!e || !e.value) && function (t) { try { if (!s()) return; !function (t) { const e = s(); if (!e) return; const n = u(t); if (!n) return; const o = n.value; o && e.removeItem(o) }(t); const e = Array.from(Array(32), (() => Math.random().toString(36)[2])).join(''); !function (t, e) { u(t) || t.append(Object.assign(document.createElement('input'), { type: 'hidden', name: r })), t.elements[r].value = e }(t, e), function (t, e) { const n = s(); if (!n) return; const r = [...t.querySelectorAll(`input[type='${o}']`)].map((({ name: t }) => t)), u = [...c, ...r], a = {}; for (const [o, c] of new FormData(t).entries()) u.includes(o) || (a[o] = c); n.setItem(e, JSON.stringify({ [i]: 1, action: t.action, data: a })) }(t, e) } catch (e) { console.error('failed to persist form', e) } }(t) } const f = t => { if ('true' === t.dataset.persistBound) return; const e = function (t, e) { const n = function (t) { return 'function' == typeof t.submit ? t.submit : HTMLFormElement.prototype.submit }(t).bind(t); return function () { let t; return () => { t || (t = !0, (() => { try { e(), n() } catch (t) { (t => { console.error('form submit failed', t) })(t) } })(), setTimeout((() => t = !1), 250)) } }() }(t, (() => { m(t) })); !function (t, e) { if ('function' == typeof t.submit && 'function' == typeof e) try { t.submit = e } catch { } }(t, e), t.addEventListener('submit', (t => { t.preventDefault(), e() })), t.dataset.persistBound = 'true' }; !function () { function t(t) { const e = (t => { const e = t.target; return e instanceof HTMLFormElement ? e : e && e.form })(t); e && m(e) } document.addEventListener('submit', t), document.addEventListener('DOMContentLoaded', (() => { const e = a(); for (const t of e) f(t); var n; n = document.body, new window.MutationObserver((t => { for (const e of t) if ('childList' === e.type && e.addedNodes.length) for (const t of e.addedNodes) 1 === t.nodeType && 'FORM' === t.tagName && a().includes(t) && f(t) })).observe(n, { childList: !0, subtree: !0, attributes: !1 }), document.removeEventListener('submit', t) })) }() }();</script>
    <script integrity="sha256-52AcMU7V7pcBOXWImdc/TAGTFKeNjmkeM1Pvks/DTgc="
        data-source-attribution="shopify.loadfeatures" defer="defer"
        src="//section.store/cdn/shopifycloud/storefront/assets/storefront/load_feature-81c60534.js"
        crossorigin="anonymous"></script>
    <script data-source-attribution="shopify.dynamic_checkout.dynamic.init">var Shopify = Shopify || {}; Shopify.PaymentButton = Shopify.PaymentButton || { isStorefrontPortableWallets: !0, init: function () { window.Shopify.PaymentButton.init = function () { }; var t = document.createElement("script"); t.src = "https://section.store/cdn/shopifycloud/portable-wallets/latest/portable-wallets.en.js", t.type = "module", document.head.appendChild(t) } };
    </script>
    <script data-source-attribution="shopify.dynamic_checkout.buyer_consent">
        function portableWalletsHideBuyerConsent(e) { var t = document.getElementById("shopify-buyer-consent"), n = document.getElementById("shopify-subscription-policy-button"); t && n && (t.classList.add("hidden"), t.setAttribute("aria-hidden", "true"), n.removeEventListener("click", e)) } function portableWalletsShowBuyerConsent(e) { var t = document.getElementById("shopify-buyer-consent"), n = document.getElementById("shopify-subscription-policy-button"); t && n && (t.classList.remove("hidden"), t.removeAttribute("aria-hidden"), n.addEventListener("click", e)) } window.Shopify?.PaymentButton && (window.Shopify.PaymentButton.hideBuyerConsent = portableWalletsHideBuyerConsent, window.Shopify.PaymentButton.showBuyerConsent = portableWalletsShowBuyerConsent);
    </script>
    <script data-source-attribution="shopify.dynamic_checkout.cart.bootstrap">document.addEventListener("DOMContentLoaded", (function () { function t() { return document.querySelector("shopify-accelerated-checkout-cart, shopify-accelerated-checkout") } if (t()) Shopify.PaymentButton.init(); else { new MutationObserver((function (e, n) { t() && (Shopify.PaymentButton.init(), n.disconnect()) })).observe(document.body, { childList: !0, subtree: !0 }) } }));
    </script>
    <script id="sections-script" data-sections="header,footer" defer="defer"
        src="//section.store/cdn/shop/t/1/compiled_assets/scripts.js?112539"></script>
    <script>window.performance && window.performance.mark && window.performance.mark('shopify.content_for_header.end');</script>


    <style data-shopify>
        @font-face {
            font-family: Montserrat;
            font-weight: 400;
            font-style: normal;
            font-display: swap;
            src: url("//section.store/cdn/fonts/montserrat/montserrat_n4.81949fa0ac9fd2021e16436151e8eaa539321637.woff2?h1=c2VjdGlvbi5zdG9yZQ&h2=c2VjdGlvbi1zdG9yZS1hcHAuYWNjb3VudC5teXNob3BpZnkuY29t&hmac=0e7de9a49ac079a04bce01488db149e4df7a2d3920960c7c9b098b7cd69cb788") format("woff2"),
                url("//section.store/cdn/fonts/montserrat/montserrat_n4.a6c632ca7b62da89c3594789ba828388aac693fe.woff?h1=c2VjdGlvbi5zdG9yZQ&h2=c2VjdGlvbi1zdG9yZS1hcHAuYWNjb3VudC5teXNob3BpZnkuY29t&hmac=a7577d830d57713f2b99c2ed7a0039ef271bdd0a1af2dfa441bbd1f13ac7f4bc") format("woff");
        }

        @font-face {
            font-family: Montserrat;
            font-weight: 700;
            font-style: normal;
            font-display: swap;
            src: url("//section.store/cdn/fonts/montserrat/montserrat_n7.3c434e22befd5c18a6b4afadb1e3d77c128c7939.woff2?h1=c2VjdGlvbi5zdG9yZQ&h2=c2VjdGlvbi1zdG9yZS1hcHAuYWNjb3VudC5teXNob3BpZnkuY29t&hmac=959c657068dd7b96f5988e3e84b03582c1a771c04a33a70bf71261cfd63c451a") format("woff2"),
                url("//section.store/cdn/fonts/montserrat/montserrat_n7.5d9fa6e2cae713c8fb539a9876489d86207fe957.woff?h1=c2VjdGlvbi5zdG9yZQ&h2=c2VjdGlvbi1zdG9yZS1hcHAuYWNjb3VudC5teXNob3BpZnkuY29t&hmac=bfb675cd1359172ec0c7deee2484df23fafc529f8e46267efd79c58de530ed5e") format("woff");
        }

        @font-face {
            font-family: Montserrat;
            font-weight: 400;
            font-style: italic;
            font-display: swap;
            src: url("//section.store/cdn/fonts/montserrat/montserrat_i4.5a4ea298b4789e064f62a29aafc18d41f09ae59b.woff2?h1=c2VjdGlvbi5zdG9yZQ&h2=c2VjdGlvbi1zdG9yZS1hcHAuYWNjb3VudC5teXNob3BpZnkuY29t&hmac=3ada2f1f7c642fc4109cc5990c6c88430c15dfcd7bee31642e7087760f199c46") format("woff2"),
                url("//section.store/cdn/fonts/montserrat/montserrat_i4.072b5869c5e0ed5b9d2021e4c2af132e16681ad2.woff?h1=c2VjdGlvbi5zdG9yZQ&h2=c2VjdGlvbi1zdG9yZS1hcHAuYWNjb3VudC5teXNob3BpZnkuY29t&hmac=c630bcbb4adb0998a3a2990601c71252f90e98b2135a22cc4098aeea18a8f575") format("woff");
        }

        @font-face {
            font-family: Montserrat;
            font-weight: 700;
            font-style: italic;
            font-display: swap;
            src: url("//section.store/cdn/fonts/montserrat/montserrat_i7.a0d4a463df4f146567d871890ffb3c80408e7732.woff2?h1=c2VjdGlvbi5zdG9yZQ&h2=c2VjdGlvbi1zdG9yZS1hcHAuYWNjb3VudC5teXNob3BpZnkuY29t&hmac=1dff724cd4fd79f26ed551d6cdd17d6e12759738572a218f4d617a913e00388d") format("woff2"),
                url("//section.store/cdn/fonts/montserrat/montserrat_i7.f6ec9f2a0681acc6f8152c40921d2a4d2e1a2c78.woff?h1=c2VjdGlvbi5zdG9yZQ&h2=c2VjdGlvbi1zdG9yZS1hcHAuYWNjb3VudC5teXNob3BpZnkuY29t&hmac=d0c03f1e89f74f2b7d700ecef2bd7a37617a2201cfa021b94bf79e95f9656e33") format("woff");
        }

        @font-face {
            font-family: Montserrat;
            font-weight: 700;
            font-style: normal;
            font-display: swap;
            src: url("//section.store/cdn/fonts/montserrat/montserrat_n7.3c434e22befd5c18a6b4afadb1e3d77c128c7939.woff2?h1=c2VjdGlvbi5zdG9yZQ&h2=c2VjdGlvbi1zdG9yZS1hcHAuYWNjb3VudC5teXNob3BpZnkuY29t&hmac=959c657068dd7b96f5988e3e84b03582c1a771c04a33a70bf71261cfd63c451a") format("woff2"),
                url("//section.store/cdn/fonts/montserrat/montserrat_n7.5d9fa6e2cae713c8fb539a9876489d86207fe957.woff?h1=c2VjdGlvbi5zdG9yZQ&h2=c2VjdGlvbi1zdG9yZS1hcHAuYWNjb3VudC5teXNob3BpZnkuY29t&hmac=bfb675cd1359172ec0c7deee2484df23fafc529f8e46267efd79c58de530ed5e") format("woff");
        }


        :root {
            --font-body-family: Montserrat, sans-serif;
            --font-body-style: normal;
            --font-body-weight: 400;
            --font-body-weight-bold: 700;

            --font-heading-family: Montserrat, sans-serif;
            --font-heading-style: normal;
            --font-heading-weight: 700;

            --font-body-scale: 1.0;
            --font-heading-scale: 1.0;

            --color-base-text: 18, 18, 18;
            --color-shadow: 18, 18, 18;
            --color-base-background-1: 255, 255, 255;
            --color-base-background-2: 243, 243, 243;
            --color-base-solid-button-labels: 255, 255, 255;
            --color-base-outline-button-labels: 18, 18, 17;
            --color-base-accent-1: 18, 18, 18;
            --color-base-accent-2: 51, 79, 180;
            --payment-terms-background-color: #ffffff;

            --gradient-base-background-1: #ffffff;
            --gradient-base-background-2: #f3f3f3;
            --gradient-base-accent-1: #121212;
            --gradient-base-accent-2: #334fb4;

            --media-padding: px;
            --media-border-opacity: 0.05;
            --media-border-width: 1px;
            --media-radius: 0px;
            --media-shadow-opacity: 0.0;
            --media-shadow-horizontal-offset: 0px;
            --media-shadow-vertical-offset: 4px;
            --media-shadow-blur-radius: 5px;
            --media-shadow-visible: 0;

            --page-width: 120rem;
            --page-width-margin: 0rem;

            --product-card-image-padding: 0.0rem;
            --product-card-corner-radius: 0.0rem;
            --product-card-text-alignment: left;
            --product-card-border-width: 0.0rem;
            --product-card-border-opacity: 0.1;
            --product-card-shadow-opacity: 0.0;
            --product-card-shadow-visible: 0;
            --product-card-shadow-horizontal-offset: 0.0rem;
            --product-card-shadow-vertical-offset: 0.4rem;
            --product-card-shadow-blur-radius: 0.5rem;

            --collection-card-image-padding: 0.0rem;
            --collection-card-corner-radius: 0.0rem;
            --collection-card-text-alignment: left;
            --collection-card-border-width: 0.0rem;
            --collection-card-border-opacity: 0.1;
            --collection-card-shadow-opacity: 0.0;
            --collection-card-shadow-visible: 0;
            --collection-card-shadow-horizontal-offset: 0.0rem;
            --collection-card-shadow-vertical-offset: 0.4rem;
            --collection-card-shadow-blur-radius: 0.5rem;

            --blog-card-image-padding: 0.0rem;
            --blog-card-corner-radius: 0.0rem;
            --blog-card-text-alignment: left;
            --blog-card-border-width: 0.0rem;
            --blog-card-border-opacity: 0.1;
            --blog-card-shadow-opacity: 0.0;
            --blog-card-shadow-visible: 0;
            --blog-card-shadow-horizontal-offset: 0.0rem;
            --blog-card-shadow-vertical-offset: 0.4rem;
            --blog-card-shadow-blur-radius: 0.5rem;

            --badge-corner-radius: 4.0rem;

            --popup-border-width: 1px;
            --popup-border-opacity: 0.1;
            --popup-corner-radius: 0px;
            --popup-shadow-opacity: 0.0;
            --popup-shadow-horizontal-offset: 0px;
            --popup-shadow-vertical-offset: 4px;
            --popup-shadow-blur-radius: 5px;

            --drawer-border-width: 1px;
            --drawer-border-opacity: 0.1;
            --drawer-shadow-opacity: 0.0;
            --drawer-shadow-horizontal-offset: 0px;
            --drawer-shadow-vertical-offset: 4px;
            --drawer-shadow-blur-radius: 5px;

            --spacing-sections-desktop: 0px;
            --spacing-sections-mobile: 0px;

            --grid-desktop-vertical-spacing: 8px;
            --grid-desktop-horizontal-spacing: 8px;
            --grid-mobile-vertical-spacing: 4px;
            --grid-mobile-horizontal-spacing: 4px;

            --text-boxes-border-opacity: 0.1;
            --text-boxes-border-width: 0px;
            --text-boxes-radius: 0px;
            --text-boxes-shadow-opacity: 0.0;
            --text-boxes-shadow-visible: 0;
            --text-boxes-shadow-horizontal-offset: 0px;
            --text-boxes-shadow-vertical-offset: 4px;
            --text-boxes-shadow-blur-radius: 5px;

            --buttons-radius: 0px;
            --buttons-radius-outset: 0px;
            --buttons-border-width: 1px;
            --buttons-border-opacity: 1.0;
            --buttons-shadow-opacity: 0.0;
            --buttons-shadow-visible: 0;
            --buttons-shadow-horizontal-offset: 0px;
            --buttons-shadow-vertical-offset: 4px;
            --buttons-shadow-blur-radius: 5px;
            --buttons-border-offset: 0px;

            --inputs-radius: 0px;
            --inputs-border-width: 1px;
            --inputs-border-opacity: 0.55;
            --inputs-shadow-opacity: 0.0;
            --inputs-shadow-horizontal-offset: 0px;
            --inputs-margin-offset: 0px;
            --inputs-shadow-vertical-offset: 4px;
            --inputs-shadow-blur-radius: 5px;
            --inputs-radius-outset: 0px;

            --variant-pills-radius: 40px;
            --variant-pills-border-width: 1px;
            --variant-pills-border-opacity: 0.55;
            --variant-pills-shadow-opacity: 0.0;
            --variant-pills-shadow-horizontal-offset: 0px;
            --variant-pills-shadow-vertical-offset: 4px;
            --variant-pills-shadow-blur-radius: 5px;
        }

        *,
        *::before,
        *::after {
            box-sizing: inherit;
        }

        html {
            box-sizing: border-box;
            font-size: calc(var(--font-body-scale) * 62.5%);
            height: 100%;
        }

        body {
            display: grid;
            grid-template-rows: auto auto 1fr auto;
            grid-template-columns: 100%;
            min-height: 100%;
            margin: 0;
            font-size: 1.5rem;
            letter-spacing: 0.06rem;
            line-height: calc(1 + 0.8 / var(--font-body-scale));
            font-family: var(--font-body-family);
            font-style: var(--font-body-style);
            font-weight: var(--font-body-weight);
        }

        @media screen and (min-width: 750px) {
            body {
                font-size: 1.6rem;
            }
        }
    </style>

    <link href="//section.store/cdn/shop/t/1/assets/base.css?v=17651648020910273441728371957" rel="stylesheet"
        type="text/css" media="all" />
    <link rel="preload" as="font"
        href="//section.store/cdn/fonts/montserrat/montserrat_n4.81949fa0ac9fd2021e16436151e8eaa539321637.woff2?h1=c2VjdGlvbi5zdG9yZQ&h2=c2VjdGlvbi1zdG9yZS1hcHAuYWNjb3VudC5teXNob3BpZnkuY29t&hmac=0e7de9a49ac079a04bce01488db149e4df7a2d3920960c7c9b098b7cd69cb788"
        type="font/woff2" crossorigin>
    <link rel="preload" as="font"
        href="//section.store/cdn/fonts/montserrat/montserrat_n7.3c434e22befd5c18a6b4afadb1e3d77c128c7939.woff2?h1=c2VjdGlvbi5zdG9yZQ&h2=c2VjdGlvbi1zdG9yZS1hcHAuYWNjb3VudC5teXNob3BpZnkuY29t&hmac=959c657068dd7b96f5988e3e84b03582c1a771c04a33a70bf71261cfd63c451a"
        type="font/woff2" crossorigin>
    <link rel="stylesheet"
        href="//section.store/cdn/shop/t/1/assets/component-predictive-search.css?v=83512081251802922551663702880"
        media="print" onload="this.media='all'">
    <script>document.documentElement.className = document.documentElement.className.replace('no-js', 'js');
        if (Shopify.designMode) {
            document.documentElement.classList.add('shopify-design-mode');
        }
    </script>
    <link href="https://monorail-edge.shopifysvc.com" rel="dns-prefetch">
    <script>(function () { if ("sendBeacon" in navigator && "performance" in window) { try { var session_token_from_headers = performance.getEntriesByType('navigation')[0].serverTiming.find(x => x.name == '_s').description; } catch { var session_token_from_headers = undefined; } var session_cookie_matches = document.cookie.match(/_shopify_s=([^;]*)/); var session_token_from_cookie = session_cookie_matches && session_cookie_matches.length === 2 ? session_cookie_matches[1] : ""; var session_token = session_token_from_headers || session_token_from_cookie || ""; function handle_abandonment_event(e) { var entries = performance.getEntries().filter(function (entry) { return /monorail-edge.shopifysvc.com/.test(entry.name); }); if (!window.abandonment_tracked && entries.length === 0) { window.abandonment_tracked = true; var currentMs = Date.now(); var navigation_start = performance.timing.navigationStart; var payload = { shop_id: ***********, url: window.location.href, navigation_start, duration: currentMs - navigation_start, session_token, page_type: "index" }; window.navigator.sendBeacon("https://monorail-edge.shopifysvc.com/v1/produce", JSON.stringify({ schema_id: "online_store_buyer_site_abandonment/1.1", payload: payload, metadata: { event_created_at_ms: currentMs, event_sent_at_ms: currentMs } })); } } window.addEventListener('pagehide', handle_abandonment_event); } }());</script>
    <script
        id="web-pixels-manager-setup">(function e(e, d, r, n, o, i) { if (void 0 === i && (i = {}), !Boolean(null === (t = null === (a = window.Shopify) || void 0 === a ? void 0 : a.analytics) || void 0 === t ? void 0 : t.replayQueue)) { var a, t; window.Shopify = window.Shopify || {}; var s = window.Shopify; s.analytics = s.analytics || {}; var l = s.analytics; l.replayQueue = [], l.publish = function (e, d, r) { return l.replayQueue.push([e, d, r]), !0 }; try { self.performance.mark("wpm:start") } catch (e) { } var u = function () { var e = { modern: /Edge?\/(1{2}[4-9]|1[2-9]\d|[2-9]\d{2}|\d{4,})\.\d+(\.\d+|)|Firefox\/(1{2}[4-9]|1[2-9]\d|[2-9]\d{2}|\d{4,})\.\d+(\.\d+|)|Chrom(ium|e)\/(9{2}|\d{3,})\.\d+(\.\d+|)|(Maci|X1{2}).+ Version\/(15\.\d+|(1[6-9]|[2-9]\d|\d{3,})\.\d+)([,.]\d+|)( \(\w+\)|)( Mobile\/\w+|) Safari\/|Chrome.+OPR\/(9{2}|\d{3,})\.\d+\.\d+|(CPU[ +]OS|iPhone[ +]OS|CPU[ +]iPhone|CPU IPhone OS|CPU iPad OS)[ +]+(15[._]\d+|(1[6-9]|[2-9]\d|\d{3,})[._]\d+)([._]\d+|)|Android:?[ /-](13[3-9]|1[4-9]\d|[2-9]\d{2}|\d{4,})(\.\d+|)(\.\d+|)|Android.+Firefox\/(13[5-9]|1[4-9]\d|[2-9]\d{2}|\d{4,})\.\d+(\.\d+|)|Android.+Chrom(ium|e)\/(13[3-9]|1[4-9]\d|[2-9]\d{2}|\d{4,})\.\d+(\.\d+|)|SamsungBrowser\/([2-9]\d|\d{3,})\.\d+/, legacy: /Edge?\/(1[6-9]|[2-9]\d|\d{3,})\.\d+(\.\d+|)|Firefox\/(5[4-9]|[6-9]\d|\d{3,})\.\d+(\.\d+|)|Chrom(ium|e)\/(5[1-9]|[6-9]\d|\d{3,})\.\d+(\.\d+|)([\d.]+$|.*Safari\/(?![\d.]+ Edge\/[\d.]+$))|(Maci|X1{2}).+ Version\/(10\.\d+|(1[1-9]|[2-9]\d|\d{3,})\.\d+)([,.]\d+|)( \(\w+\)|)( Mobile\/\w+|) Safari\/|Chrome.+OPR\/(3[89]|[4-9]\d|\d{3,})\.\d+\.\d+|(CPU[ +]OS|iPhone[ +]OS|CPU[ +]iPhone|CPU IPhone OS|CPU iPad OS)[ +]+(10[._]\d+|(1[1-9]|[2-9]\d|\d{3,})[._]\d+)([._]\d+|)|Android:?[ /-](13[3-9]|1[4-9]\d|[2-9]\d{2}|\d{4,})(\.\d+|)(\.\d+|)|Mobile Safari.+OPR\/([89]\d|\d{3,})\.\d+\.\d+|Android.+Firefox\/(13[5-9]|1[4-9]\d|[2-9]\d{2}|\d{4,})\.\d+(\.\d+|)|Android.+Chrom(ium|e)\/(13[3-9]|1[4-9]\d|[2-9]\d{2}|\d{4,})\.\d+(\.\d+|)|Android.+(UC? ?Browser|UCWEB|U3)[ /]?(15\.([5-9]|\d{2,})|(1[6-9]|[2-9]\d|\d{3,})\.\d+)\.\d+|SamsungBrowser\/(5\.\d+|([6-9]|\d{2,})\.\d+)|Android.+MQ{2}Browser\/(14(\.(9|\d{2,})|)|(1[5-9]|[2-9]\d|\d{3,})(\.\d+|))(\.\d+|)|K[Aa][Ii]OS\/(3\.\d+|([4-9]|\d{2,})\.\d+)(\.\d+|)/ }, d = e.modern, r = e.legacy, n = navigator.userAgent; return n.match(d) ? "modern" : n.match(r) ? "legacy" : "unknown" }(), c = "modern" === u ? "modern" : "legacy", f = (null != o ? o : { modern: "", legacy: "" })[c], m = function (e) { return [e.baseUrl, "/wpm", "/b", e.hashVersion, "modern" === e.buildTarget ? "m" : "l", ".js"].join("") }({ baseUrl: r, hashVersion: n, buildTarget: c }), p = function (e) { var d = e.version, r = e.bundleTarget, n = e.surface, o = e.pageUrl, i = e.monorailEndpoint; return { emit: function (e) { var a = e.status, t = e.errorMsg, s = (new Date).getTime(), l = JSON.stringify({ metadata: { event_sent_at_ms: s }, events: [{ schema_id: "web_pixels_manager_load/3.1", payload: { version: d, bundle_target: r, page_url: o, status: a, surface: n, error_msg: t }, metadata: { event_created_at_ms: s } }] }); if (!i) return console && console.warn && console.warn("[Web Pixels Manager] No Monorail endpoint provided, skipping logging."), !1; try { return self.navigator.sendBeacon.bind(self.navigator)(i, l) } catch (e) { } var u = new XMLHttpRequest; try { return u.open("POST", i, !0), u.setRequestHeader("Content-Type", "text/plain"), u.send(l), !0 } catch (e) { return console && console.warn && console.warn("[Web Pixels Manager] Got an unhandled error while logging to Monorail."), !1 } } } }({ version: n, bundleTarget: u, surface: e.surface, pageUrl: self.location.href, monorailEndpoint: e.monorailEndpoint }); try { i.browserTarget = u, function (e) { var d = e.src, r = e.async, n = void 0 === r || r, o = e.onload, i = e.onerror, a = e.sri, t = e.scriptDataAttributes, s = void 0 === t ? {} : t, l = document.createElement("script"), u = document.querySelector("head"), c = document.querySelector("body"); if (l.async = n, l.src = d, a && (l.integrity = a, l.crossOrigin = "anonymous"), s) for (var f in s) if (Object.prototype.hasOwnProperty.call(s, f)) try { l.dataset[f] = s[f] } catch (e) { } if (o && l.addEventListener("load", o), i && l.addEventListener("error", i), u) u.appendChild(l); else { if (!c) throw new Error("Did not find a head or body element to append the script"); c.appendChild(l) } }({ src: m, async: !0, onload: function () { if (!function () { var e, d; return Boolean(null === (d = null === (e = window.Shopify) || void 0 === e ? void 0 : e.analytics) || void 0 === d ? void 0 : d.initialized) }()) { var r = window.webPixelsManager.init(e) || void 0; if (r) { d(r); var n = window.Shopify.analytics; n.replayQueue.forEach((function (e) { var d = e[0], n = e[1], o = e[2]; r.publishCustomEvent(d, n, o) })), n.replayQueue = [], n.publish = r.publishCustomEvent, n.visitor = r.visitor, n.initialized = !0 } } }, onerror: function () { return p.emit({ status: "failed", errorMsg: "".concat(m, " has failed to load") }) }, sri: function (e) { var d = /^sha384-[A-Za-z0-9+/=]+$/; return "string" == typeof e && d.test(e) }(f) ? f : "", scriptDataAttributes: i }), p.emit({ status: "loading" }) } catch (e) { p.emit({ status: "failed", errorMsg: (null == e ? void 0 : e.message) || "Unknown error" }) } } })({ shopId: ***********, storefrontBaseUrl: "https://section.store", extensionsBaseUrl: "https://extensions.shopifycdn.com/cdn/shopifycloud/web-pixels-manager", monorailEndpoint: "https://monorail-edge.shopifysvc.com/unstable/produce_batch", surface: "storefront-renderer", enabledBetaFlags: ["ac843a20"], webPixelsConfigList: [{ "id": "2143879497", "configuration": "{\"webPixelName\":\"Judge.me\"}", "eventPayloadVersion": "v1", "runtimeContext": "STRICT", "scriptVersion": "34ad157958823915625854214640f0bf", "type": "APP", "apiClientId": 683015, "privacyPurposes": ["ANALYTICS"] }, { "id": "2030666057", "configuration": "{\"store\":\"section-store-app.myshopify.com\"}", "eventPayloadVersion": "v1", "runtimeContext": "STRICT", "scriptVersion": "28723c4f9305a54bdd7b9effc395c73b", "type": "APP", "apiClientId": 3135504385, "privacyPurposes": ["ANALYTICS", "MARKETING", "SALE_OF_DATA"] }, { "id": "shopify-app-pixel", "configuration": "{}", "eventPayloadVersion": "v1", "runtimeContext": "STRICT", "scriptVersion": "0440", "apiClientId": "shopify-pixel", "type": "APP", "privacyPurposes": ["ANALYTICS", "MARKETING"] }, { "id": "shopify-custom-pixel", "eventPayloadVersion": "v1", "runtimeContext": "LAX", "scriptVersion": "0440", "apiClientId": "shopify-pixel", "type": "CUSTOM", "privacyPurposes": ["ANALYTICS", "MARKETING"] }], isMerchantRequest: false, initData: { "shop": { "name": "Section Store", "paymentSettings": { "currencyCode": "USD" }, "myshopifyDomain": "section-store-app.myshopify.com", "countryCode": "DK", "storefrontUrl": "https://section.store" }, "customer": null, "cart": null, "checkout": null, "productVariants": [], "purchasingCompany": null }, }, function pageEvents(webPixelsManagerAPI) { webPixelsManagerAPI.publish("page_viewed", {}); }, "https://section.store/cdn", "3a2385cbw462f4b4cpf5703495mae1b83e5", { "modern": "", "legacy": "" }, { "shopId": "***********", "storefrontBaseUrl": "https://section.store", "extensionBaseUrl": "https://extensions.shopifycdn.com/cdn/shopifycloud/web-pixels-manager", "surface": "storefront-renderer", "enabledBetaFlags": "[\"ac843a20\"]", "isMerchantRequest": "false", "hashVersion": "3a2385cbw462f4b4cpf5703495mae1b83e5", "publish": "custom" });</script>
    <script>
        window.ShopifyAnalytics = window.ShopifyAnalytics || {};
        window.ShopifyAnalytics.meta = window.ShopifyAnalytics.meta || {};
        window.ShopifyAnalytics.meta.currency = 'USD';
        var meta = { "page": { "pageType": "home" } };
        for (var attr in meta) {
            window.ShopifyAnalytics.meta[attr] = meta[attr];
        }
    </script>
    <script class="analytics">
        (function () {
            var customDocumentWrite = function (content) {
                var jquery = null;

                if (window.jQuery) {
                    jquery = window.jQuery;
                } else if (window.Checkout && window.Checkout.$) {
                    jquery = window.Checkout.$;
                }

                if (jquery) {
                    jquery('body').append(content);
                }
            };

            var hasLoggedConversion = function (token) {
                if (token) {
                    return document.cookie.indexOf('loggedConversion=' + token) !== -1;
                }
                return false;
            }

            var setCookieIfConversion = function (token) {
                if (token) {
                    var twoMonthsFromNow = new Date(Date.now());
                    twoMonthsFromNow.setMonth(twoMonthsFromNow.getMonth() + 2);

                    document.cookie = 'loggedConversion=' + token + '; expires=' + twoMonthsFromNow;
                }
            }

            var trekkie = window.ShopifyAnalytics.lib = window.trekkie = window.trekkie || [];
            if (trekkie.integrations) {
                return;
            }
            trekkie.methods = [
                'identify',
                'page',
                'ready',
                'track',
                'trackForm',
                'trackLink'
            ];
            trekkie.factory = function (method) {
                return function () {
                    var args = Array.prototype.slice.call(arguments);
                    args.unshift(method);
                    trekkie.push(args);
                    return trekkie;
                };
            };
            for (var i = 0; i < trekkie.methods.length; i++) {
                var key = trekkie.methods[i];
                trekkie[key] = trekkie.factory(key);
            }
            trekkie.load = function (config) {
                trekkie.config = config || {};
                trekkie.config.initialDocumentCookie = document.cookie;
                var first = document.getElementsByTagName('script')[0];
                var script = document.createElement('script');
                script.type = 'text/javascript';
                script.onerror = function (e) {
                    var scriptFallback = document.createElement('script');
                    scriptFallback.type = 'text/javascript';
                    scriptFallback.onerror = function (error) {
                        var Monorail = {
                            produce: function produce(monorailDomain, schemaId, payload) {
                                var currentMs = new Date().getTime();
                                var event = {
                                    schema_id: schemaId,
                                    payload: payload,
                                    metadata: {
                                        event_created_at_ms: currentMs,
                                        event_sent_at_ms: currentMs
                                    }
                                };
                                return Monorail.sendRequest("https://" + monorailDomain + "/v1/produce", JSON.stringify(event));
                            },
                            sendRequest: function sendRequest(endpointUrl, payload) {
                                // Try the sendBeacon API
                                if (window && window.navigator && typeof window.navigator.sendBeacon === 'function' && typeof window.Blob === 'function' && !Monorail.isIos12()) {
                                    var blobData = new window.Blob([payload], {
                                        type: 'text/plain'
                                    });

                                    if (window.navigator.sendBeacon(endpointUrl, blobData)) {
                                        return true;
                                    } // sendBeacon was not successful

                                } // XHR beacon

                                var xhr = new XMLHttpRequest();

                                try {
                                    xhr.open('POST', endpointUrl);
                                    xhr.setRequestHeader('Content-Type', 'text/plain');
                                    xhr.send(payload);
                                } catch (e) {
                                    console.log(e);
                                }

                                return false;
                            },
                            isIos12: function isIos12() {
                                return window.navigator.userAgent.lastIndexOf('iPhone; CPU iPhone OS 12_') !== -1 || window.navigator.userAgent.lastIndexOf('iPad; CPU OS 12_') !== -1;
                            }
                        };
                        Monorail.produce('monorail-edge.shopifysvc.com',
                            'trekkie_storefront_load_errors/1.1',
                            {
                                shop_id: ***********,
                                theme_id: 136403255545,
                                app_name: "storefront",
                                context_url: window.location.href,
                                source_url: "//section.store/cdn/s/trekkie.storefront.85527fa5792f2e0cb2c2b51738712be68613edc8.min.js"
                            });

                    };
                    scriptFallback.async = true;
                    scriptFallback.src = '//section.store/cdn/s/trekkie.storefront.85527fa5792f2e0cb2c2b51738712be68613edc8.min.js';
                    first.parentNode.insertBefore(scriptFallback, first);
                };
                script.async = true;
                script.src = '//section.store/cdn/s/trekkie.storefront.85527fa5792f2e0cb2c2b51738712be68613edc8.min.js';
                first.parentNode.insertBefore(script, first);
            };
            trekkie.load(
                { "Trekkie": { "appName": "storefront", "development": false, "defaultAttributes": { "shopId": ***********, "isMerchantRequest": null, "themeId": 136403255545, "themeCityHash": "11823669132356797107", "contentLanguage": "en", "currency": "USD", "eventMetadataId": "828c8423-e842-491c-a765-31f530a9851c" }, "isServerSideCookieWritingEnabled": true, "monorailRegion": "shop_domain" }, "Session Attribution": {}, "S2S": { "facebookCapiEnabled": false, "source": "trekkie-storefront-renderer", "apiClientId": 580111 } }
            );

            var loaded = false;
            trekkie.ready(function () {
                if (loaded) return;
                loaded = true;

                window.ShopifyAnalytics.lib = window.trekkie;

                var originalDocumentWrite = document.write;
                document.write = customDocumentWrite;
                try { window.ShopifyAnalytics.merchantGoogleAnalytics.call(this); } catch (error) { };
                document.write = originalDocumentWrite;

                window.ShopifyAnalytics.lib.page(null, { "pageType": "home", "shopifyEmitted": true });

                var match = window.location.pathname.match(/checkouts\/(.+)\/(thank_you|post_purchase)/)
                var token = match ? match[1] : undefined;
                if (!hasLoggedConversion(token)) {
                    setCookieIfConversion(token);

                }
            });


            var eventsListenerScript = document.createElement('script');
            eventsListenerScript.async = true;
            eventsListenerScript.src = "//section.store/cdn/shopifycloud/storefront/assets/shop_events_listener-8675b082.js";
            document.getElementsByTagName('head')[0].appendChild(eventsListenerScript);

        })();</script>
    <script defer src="https://section.store/cdn/shopifycloud/perf-kit/shopify-perf-kit-1.6.6.min.js"
        data-application="storefront-renderer" data-shop-id="***********" data-render-region="gcp-europe-west4"
        data-page-type="index" data-theme-instance-id="136403255545" data-theme-name="Dawn" data-theme-version="7.0.0"
        data-monorail-region="shop_domain" data-resource-timing-sampling-rate="10" data-shs="true"></script>
</head>

<body class="gradient">
    <a class="skip-to-content-link button visually-hidden" href="#MainContent">
        Skip to content
    </a>

    <div id="shopify-section-announcement-bar" class="shopify-section">
    </div>

    <div id="shopify-section-header" class="shopify-section section-header">
        <link rel="stylesheet"
            href="//section.store/cdn/shop/t/1/assets/component-list-menu.css?v=151968516119678728991663702890"
            media="print" onload="this.media='all'">
        <link rel="stylesheet"
            href="//section.store/cdn/shop/t/1/assets/component-search.css?v=96455689198851321781663702873"
            media="print" onload="this.media='all'">
        <link rel="stylesheet"
            href="//section.store/cdn/shop/t/1/assets/component-menu-drawer.css?v=182311192829367774911663702877"
            media="print" onload="this.media='all'">
        <link rel="stylesheet"
            href="//section.store/cdn/shop/t/1/assets/component-cart-notification.css?v=183358051719344305851663702867"
            media="print" onload="this.media='all'">
        <link rel="stylesheet"
            href="//section.store/cdn/shop/t/1/assets/component-cart-items.css?v=23917223812499722491663702906"
            media="print" onload="this.media='all'">
        <link rel="stylesheet"
            href="//section.store/cdn/shop/t/1/assets/component-price.css?v=65402837579211014041663702876" media="print"
            onload="this.media='all'">
        <link rel="stylesheet"
            href="//section.store/cdn/shop/t/1/assets/component-loading-overlay.css?v=167310470843593579841663702910"
            media="print" onload="this.media='all'"><noscript>
            <link href="//section.store/cdn/shop/t/1/assets/component-list-menu.css?v=151968516119678728991663702890"
                rel="stylesheet" type="text/css" media="all" />
        </noscript>
        <noscript>
            <link href="//section.store/cdn/shop/t/1/assets/component-search.css?v=96455689198851321781663702873"
                rel="stylesheet" type="text/css" media="all" />
        </noscript>
        <noscript>
            <link href="//section.store/cdn/shop/t/1/assets/component-menu-drawer.css?v=182311192829367774911663702877"
                rel="stylesheet" type="text/css" media="all" />
        </noscript>
        <noscript>
            <link
                href="//section.store/cdn/shop/t/1/assets/component-cart-notification.css?v=183358051719344305851663702867"
                rel="stylesheet" type="text/css" media="all" />
        </noscript>
        <noscript>
            <link href="//section.store/cdn/shop/t/1/assets/component-cart-items.css?v=23917223812499722491663702906"
                rel="stylesheet" type="text/css" media="all" />
        </noscript>

        <style>
            header-drawer {
                justify-self: start;
                margin-left: -1.2rem;
            }

            .header__heading-logo {
                max-width: 190px;
            }

            @media screen and (min-width: 990px) {
                header-drawer {
                    display: none;
                }
            }

            .menu-drawer-container {
                display: flex;
            }

            .list-menu {
                list-style: none;
                padding: 0;
                margin: 0;
            }

            .list-menu--inline {
                display: inline-flex;
                flex-wrap: wrap;
            }

            summary.list-menu__item {
                padding-right: 2.7rem;
            }

            .list-menu__item {
                display: flex;
                align-items: center;
                line-height: calc(1 + 0.3 / var(--font-body-scale));
            }

            .list-menu__item--link {
                text-decoration: none;
                padding-bottom: 1rem;
                padding-top: 1rem;
                line-height: calc(1 + 0.8 / var(--font-body-scale));
            }

            @media screen and (min-width: 750px) {
                .list-menu__item--link {
                    padding-bottom: 0.5rem;
                    padding-top: 0.5rem;
                }
            }
        </style>
        <style data-shopify>
            .header {
                padding-top: 10px;
                padding-bottom: 10px;
            }

            .section-header {
                margin-bottom: 0px;
            }

            @media screen and (min-width: 750px) {
                .section-header {
                    margin-bottom: 0px;
                }
            }

            @media screen and (min-width: 990px) {
                .header {
                    padding-top: 20px;
                    padding-bottom: 20px;
                }
            }
        </style>
        <script src="//section.store/cdn/shop/t/1/assets/details-disclosure.js?v=153497636716254413831663702899"
            defer="defer"></script>
        <script src="//section.store/cdn/shop/t/1/assets/details-modal.js?v=4511761896672669691663702898"
            defer="defer"></script>
        <script src="//section.store/cdn/shop/t/1/assets/cart-notification.js?v=160453272920806432391663702875"
            defer="defer"></script><svg xmlns="http://www.w3.org/2000/svg" class="hidden">
            <symbol id="icon-search" viewbox="0 0 18 19" fill="none">
                <path fill-rule="evenodd" clip-rule="evenodd"
                    d="M11.03 11.68A5.784 5.784 0 112.85 3.5a5.784 5.784 0 018.18 8.18zm.26 1.12a6.78 6.78 0 11.72-.7l5.4 5.4a.5.5 0 11-.71.7l-5.41-5.4z"
                    fill="currentColor" />
            </symbol>

            <symbol id="icon-close" class="icon icon-close" fill="none" viewBox="0 0 18 17">
                <path
                    d="M.865 15.978a.5.5 0 00.707.707l7.433-7.431 7.579 7.282a.501.501 0 00.846-.37.5.5 0 00-.153-.351L9.712 8.546l7.417-7.416a.5.5 0 10-.707-.708L8.991 7.853 1.413.573a.5.5 0 10-.693.72l7.563 7.268-7.418 7.417z"
                    fill="currentColor">
            </symbol>
        </svg>
        <sticky-header class="header-wrapper color-background-1 gradient header-wrapper--border-bottom">
            <header class="header header--middle-left header--mobile-left page-width"><details-modal
                    class="header__search">
                    <details>
                        <summary
                            class="header__icon header__icon--search header__icon--summary link focus-inset modal__toggle"
                            aria-haspopup="dialog" aria-label="Search">
                            <span>
                                <svg class="modal__toggle-open icon icon-search" aria-hidden="true" focusable="false"
                                    role="presentation">
                                    <use href="#icon-search">
                                </svg>
                                <svg class="modal__toggle-close icon icon-close" aria-hidden="true" focusable="false"
                                    role="presentation">
                                    <use href="#icon-close">
                                </svg>
                            </span>
                        </summary>
                        <div class="search-modal modal__content gradient" role="dialog" aria-modal="true"
                            aria-label="Search">
                            <div class="modal-overlay"></div>
                            <div class="search-modal__content search-modal__content-bottom" tabindex="-1">
                                <predictive-search class="search-modal__form" data-loading-text="Loading...">
                                    <form action="/search" method="get" role="search" class="search search-modal__form">
                                        <div class="field">
                                            <input class="search__input field__input" id="Search-In-Modal-1"
                                                type="search" name="q" value="" placeholder="Search" role="combobox"
                                                aria-expanded="false" aria-owns="predictive-search-results-list"
                                                aria-controls="predictive-search-results-list" aria-haspopup="listbox"
                                                aria-autocomplete="list" autocorrect="off" autocomplete="off"
                                                autocapitalize="off" spellcheck="false">
                                            <label class="field__label" for="Search-In-Modal-1">Search</label>
                                            <input type="hidden" name="options[prefix]" value="last">
                                            <button class="search__button field__button" aria-label="Search">
                                                <svg class="icon icon-search" aria-hidden="true" focusable="false"
                                                    role="presentation">
                                                    <use href="#icon-search">
                                                </svg>
                                            </button>
                                        </div>
                                        <div class="predictive-search predictive-search--header" tabindex="-1"
                                            data-predictive-search>
                                            <div class="predictive-search__loading-state">
                                                <svg aria-hidden="true" focusable="false" role="presentation"
                                                    class="spinner" viewBox="0 0 66 66"
                                                    xmlns="http://www.w3.org/2000/svg">
                                                    <circle class="path" fill="none" stroke-width="6" cx="33" cy="33"
                                                        r="30"></circle>
                                                </svg>
                                            </div>
                                        </div>

                                        <span class="predictive-search-status visually-hidden" role="status"
                                            aria-hidden="true"></span>
                                    </form>
                                </predictive-search><button type="button"
                                    class="modal__close-button link link--text focus-inset" aria-label="Close">
                                    <svg class="icon icon-close" aria-hidden="true" focusable="false"
                                        role="presentation">
                                        <use href="#icon-close">
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </details>
                </details-modal>
                <h1 class="header__heading"><a href="/" class="header__heading-link link link--text focus-inset"><img
                            src="//section.store/cdn/shop/files/Frame_1_4.png?v=1663753183" alt="Section Store"
                            srcset="//section.store/cdn/shop/files/Frame_1_4.png?v=1663753183&amp;width=50 50w, //section.store/cdn/shop/files/Frame_1_4.png?v=1663753183&amp;width=100 100w, //section.store/cdn/shop/files/Frame_1_4.png?v=1663753183&amp;width=150 150w, //section.store/cdn/shop/files/Frame_1_4.png?v=1663753183&amp;width=200 200w, //section.store/cdn/shop/files/Frame_1_4.png?v=1663753183&amp;width=250 250w, //section.store/cdn/shop/files/Frame_1_4.png?v=1663753183&amp;width=300 300w, //section.store/cdn/shop/files/Frame_1_4.png?v=1663753183&amp;width=400 400w, //section.store/cdn/shop/files/Frame_1_4.png?v=1663753183&amp;width=500 500w"
                            width="190" height="46.55270655270655" class="header__heading-logo">
                    </a></h1>
                <div class="header__icons">
                    <details-modal class="header__search">
                        <details>
                            <summary
                                class="header__icon header__icon--search header__icon--summary link focus-inset modal__toggle"
                                aria-haspopup="dialog" aria-label="Search">
                                <span>
                                    <svg class="modal__toggle-open icon icon-search" aria-hidden="true"
                                        focusable="false" role="presentation">
                                        <use href="#icon-search">
                                    </svg>
                                    <svg class="modal__toggle-close icon icon-close" aria-hidden="true"
                                        focusable="false" role="presentation">
                                        <use href="#icon-close">
                                    </svg>
                                </span>
                            </summary>
                            <div class="search-modal modal__content gradient" role="dialog" aria-modal="true"
                                aria-label="Search">
                                <div class="modal-overlay"></div>
                                <div class="search-modal__content search-modal__content-bottom" tabindex="-1">
                                    <predictive-search class="search-modal__form" data-loading-text="Loading...">
                                        <form action="/search" method="get" role="search"
                                            class="search search-modal__form">
                                            <div class="field">
                                                <input class="search__input field__input" id="Search-In-Modal"
                                                    type="search" name="q" value="" placeholder="Search" role="combobox"
                                                    aria-expanded="false" aria-owns="predictive-search-results-list"
                                                    aria-controls="predictive-search-results-list"
                                                    aria-haspopup="listbox" aria-autocomplete="list" autocorrect="off"
                                                    autocomplete="off" autocapitalize="off" spellcheck="false">
                                                <label class="field__label" for="Search-In-Modal">Search</label>
                                                <input type="hidden" name="options[prefix]" value="last">
                                                <button class="search__button field__button" aria-label="Search">
                                                    <svg class="icon icon-search" aria-hidden="true" focusable="false"
                                                        role="presentation">
                                                        <use href="#icon-search">
                                                    </svg>
                                                </button>
                                            </div>
                                            <div class="predictive-search predictive-search--header" tabindex="-1"
                                                data-predictive-search>
                                                <div class="predictive-search__loading-state">
                                                    <svg aria-hidden="true" focusable="false" role="presentation"
                                                        class="spinner" viewBox="0 0 66 66"
                                                        xmlns="http://www.w3.org/2000/svg">
                                                        <circle class="path" fill="none" stroke-width="6" cx="33"
                                                            cy="33" r="30"></circle>
                                                    </svg>
                                                </div>
                                            </div>

                                            <span class="predictive-search-status visually-hidden" role="status"
                                                aria-hidden="true"></span>
                                        </form>
                                    </predictive-search><button type="button"
                                        class="search-modal__close-button modal__close-button link link--text focus-inset"
                                        aria-label="Close">
                                        <svg class="icon icon-close" aria-hidden="true" focusable="false"
                                            role="presentation">
                                            <use href="#icon-close">
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </details>
                    </details-modal><a href="https://shopify.com/***********/account?locale=en&region_country=US"
                        class="header__icon header__icon--account link focus-inset">
                        <svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" role="presentation"
                            class="icon icon-account" fill="none" viewBox="0 0 18 19">
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M6 4.5a3 3 0 116 0 3 3 0 01-6 0zm3-4a4 4 0 100 8 4 4 0 000-8zm5.58 12.15c1.12.82 1.83 2.24 1.91 4.85H1.51c.08-2.6.79-4.03 1.9-4.85C4.66 11.75 6.5 11.5 9 11.5s4.35.26 5.58 1.15zM9 10.5c-2.5 0-4.65.24-6.17 1.35C1.27 12.98.5 14.93.5 18v.5h17V18c0-3.07-.77-5.02-2.33-6.15-1.52-1.1-3.67-1.35-6.17-1.35z"
                                fill="currentColor">
                        </svg>

                        <span class="visually-hidden">Log in</span>
                    </a><a href="https://apps.shopify.com/section-factory" target="_blank" id="app-store-button">
                        <div><img id="app-store-button-inside" alt="section store download on shopify"
                                src="https://cdn.shopify.com/s/files/1/0670/4834/1753/t/1/assets/download_on_app_store.svg?v=**********">
                        </div>
                    </a>
                    <a href="/cart" class="header__icon header__icon--cart link focus-inset" id="cart-icon-bubble"><svg
                            class="icon icon-cart-empty" aria-hidden="true" focusable="false" role="presentation"
                            xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 40" fill="none">
                            <path
                                d="m15.75 11.8h-3.16l-.77 11.6a5 5 0 0 0 4.99 5.34h7.38a5 5 0 0 0 4.99-5.33l-.78-11.61zm0 1h-2.22l-.71 10.67a4 4 0 0 0 3.99 4.27h7.38a4 4 0 0 0 4-4.27l-.72-10.67h-2.22v.63a4.75 4.75 0 1 1 -9.5 0zm8.5 0h-7.5v.63a3.75 3.75 0 1 0 7.5 0z"
                                fill="currentColor" fill-rule="evenodd" />
                        </svg>
                        <span class="visually-hidden">Cart</span></a>
                </div>
            </header>
        </sticky-header>

        <script type="application/ld+json">
  {
    "@context": "http://schema.org",
    "@type": "Organization",
    "name": "Section Store",
    
      "logo": "https:\/\/section.store\/cdn\/shop\/files\/Frame_1_4.png?v=1663753183\u0026width=1404",
    
    "sameAs": [
      "",
      "",
      "",
      "",
      "https:\/\/www.tiktok.com\/@section.store",
      "",
      "",
      "https:\/\/www.youtube.com\/channel\/UCwL7vHNBc_xFZVQcH84MDvQ",
      ""
    ],
    "url": "https:\/\/section.store"
  }
</script>
        <script type="application/ld+json">
    {
      "@context": "http://schema.org",
      "@type": "WebSite",
      "name": "Section Store",
      "potentialAction": {
        "@type": "SearchAction",
        "target": "https:\/\/section.store\/search?q={search_term_string}",
        "query-input": "required name=search_term_string"
      },
      "url": "https:\/\/section.store"
    }
  </script>
    </div>

    <main id="MainContent" class="content-for-layout focus-none" role="main" tabindex="-1">
        <div id="shopify-section-template--16640583467257__16637101872cac8134" class="shopify-section">
            <style>
                .grid-container-blue {

                    margin: 32px auto 0px;
                    padding: 0px 32px;
                    max-width: 1476px;
                    display: grid;
                    grid-template-columns: repeat(6, 1fr);
                    gap: 32px;
                }


                .grid-cell-first-blue {

                    display: flex;
                    position: relative;
                    overflow: hidden;
                    width: 100%;
                    height: 464px;
                    grid-area: span 1 / span 4 / auto / auto;
                    border-radius: 16px;
                    box-sizing: border-box;

                }

                .grid-cell-third-blue {

                    display: flex;
                    align-items: center;
                    justify-content: center;
                    position: relative;
                    overflow: hidden;
                    width: 100%;
                    height: 464px;
                    grid-area: span 1 / span 2 / auto / auto;
                    border-radius: 16px;
                    box-sizing: border-box;
                }

                .grid-cell-second-blue {

                    display: flex;
                    align-items: center;
                    justify-content: center;
                    position: relative;
                    overflow: hidden;
                    width: 100%;
                    height: 464px;
                    grid-area: span 1 / span 3 / auto / auto;
                    border-radius: 16px;
                    box-sizing: border-box;

                }

                .grid-cell-full-blue {

                    grid-column: span 6 / auto;
                    background: rgb(243, 244, 246);
                    border-radius: 16px;
                    padding: 56px 24px 60px;
                    width: 100%;
                    box-sizing: border-box;
                    display: flex;
                    flex-direction: column;
                    -webkit-box-align: center;
                    align-items: center;
                    -webkit-box-pack: center;
                    justify-content: center;
                    position: relative;
                    margin-top: 0px;
                    margin-bottom: 0px;

                }

                .hero-text-blue {
                    padding: 55px 0px 0px 50px;
                    width: 100%;
                }

                .img-container-blue {

                    display: block;
                    margin-top: 32px;
                    margin-left: 10px;
                    width: 650px;
                    height: 410px;
                    margin-right: 40px;
                    align-self: flex-end;
                    position: relative;

                }

                .img-man-blue {

                    width: 400px;
                    position: absolute;

                }

                .third-text-blue {
                    padding: 30px;
                    width: 100%;
                    text-align: center !important;
                }

                .bluelight-button {

                    background: white;
                    margin: 10px;
                    padding: 15px 25px;
                    border-radius: 100px;
                    font-weight: 900;
                    font-size: 20px;
                    color: #121212;

                }

                @media screen and (max-width: 1100px) {

                    .grid-container-blue {
                        padding: 0px 16px;
                        margin: 16px auto 0px;
                        gap: 16px;
                    }

                    .grid-cell-first-blue {

                        padding: 0px 24px;
                        flex-direction: column;
                        height: auto;
                        grid-column: span 6 / auto;
                    }

                    .grid-cell-third-blue,
                    .grid-cell-second-blue {

                        padding: 0px 24px;
                        flex-direction: column;
                        height: auto;
                        grid-column: span 6 / auto;

                    }

                    .hero-text-blue {

                        display: flex;
                        flex-direction: column;
                        align-content: center;
                        margin: 0px auto;
                        padding: 40px 0px 0px;
                        width: 100%;
                        text-align: center !important;

                    }

                    .img-container-blue {

                        display: block;
                        margin-top: 10px;
                        margin-left: 0px;
                        width: auto;
                        height: 300px;
                        margin-right: 0px;
                        align-self: center;
                        position: relative;

                    }

                    .img-man-blue {
                        position: relative;
                    }

                    .third-text-blue {
                        padding: 40px 0px 20px 0px;
                    }

                }
            </style>

            <div style="font-family:'Euclidcirculara webfont',Arial,sans-serif!important;" class="">
                <div class="grid-container-blue">

                    <div class="grid-cell-first-blue" span="4" style="background:#C0E5FE">
                        <div class="hero-text-blue">
                            <h1 style="font-weight:900;margin:0px;">What is section store? 🛍</h1>
                            <p style="margin-top:30px;color: rgba(0, 0, 0, 0.7);">It's a revolutionary, disruptive and
                                innovative new techn.... 🤢<br><br>Jokes aside. Section Store is what the name implies.
                                A store full of sections. Shopify exclusive. Browse and find sections you need for your
                                shop. Add to any theme. Edit from Shopify's theme editor. Yes it's dead simple.</p>
                        </div>
                        <div class="img-container-blue">
                            <img class="img-man-blue"
                                src="https://cdn.shopify.com/s/files/1/0670/4834/1753/t/1/assets/19.png?v=1663765899">
                        </div>
                    </div>

                    <div class="grid-cell-third-blue" span="2" style="background:rgba(142, 238, 185, 0.6)">
                        <div class="third-text-blue">
                            <h1 style="font-weight:900;font-size:30px;">⚠️ Not a page builder</h1>
                            <p style="margin-top:30px;color: rgba(0, 0, 0, 0.7);">Let's be clear what we're not. With
                                Section Store you add new sections to your existing theme. That means you can keep using
                                Shopify's page builder; the theme editor. Add sections to your home, product, cart or
                                any page.<br><br>No steep learning curve here. We're all about the Shopify way of doing
                                things.</p>
                        </div>
                    </div>
                    <div class="grid-cell-third-blue" span="2" style="background:#FFEACB;">
                        <div class="third-text-blue">
                            <h1 style="font-weight:900;font-size:30px;">Lightning fast ⚡</h1>
                            <p style="margin-top:30px;color: rgba(0, 0, 0, 0.7);">No scripts, no left-over code, no
                                slowing-down, just wrooom 🏎.<br><br>Keep or even improve page-speed. Some apps could
                                just have been a section. Replace apps with our sections. Use our instafeed, FAQ, trust
                                badges etc. sections, instead of equivalent apps for each.</p>
                        </div>
                    </div>
                    <div class="grid-cell-third-blue" span="2" style="background:rgba(255, 177, 175, 0.5)">
                        <div class="third-text-blue">
                            <h1 style="font-weight:900;font-size:30px;">500+ Sections 👨‍💻</h1>
                            <p style="margin-top:30px;color: rgba(0, 0, 0, 0.7);">We work tirelessly so you can design &
                                customise your Shopify storefront with ease.<br><br>New sections added every week. All
                                2.0 ready (add to any page).</p>
                        </div>
                    </div>
                    <div class="grid-cell-third-blue" span="2" style="background:#D5D4F5">
                        <div class="third-text-blue">
                            <h1 style="font-weight:900;font-size:40px;"><span
                                    style="font-size:55px">❤️‍🔥</span><br><br>Buy once. Own forever.</h1>
                            <p style="margin-top:30px;color: rgba(0, 0, 0, 0.7);">An old flame never dies. Same way your
                                sections last forever. No recurring payments.<br>Almost all sections are priced from
                                0-$9 each.<br>Simple, cheap and transparent pricing.</p>
                        </div>
                    </div>
                    <div class="grid-cell-second-blue" span="2" style="background:rgba(142, 238, 185, 0.6)">
                        <div class="third-text-blue">
                            <h1 style="font-weight:900;font-size:30px;">Save money and time ⏱💸</h1>
                            <p style="margin-top:30px;color: rgba(0, 0, 0, 0.7);">Imagine this: You want to add a new
                                section to a page, but your theme doesn't have one you like. Do you buy a new theme,
                                build your page from scratch with a page builder, hire a developer or find another app
                                with monthly payment?<br><br>No, no, no & no. You browse Section Store and find that
                                section you need. If we don't have it already you call us (or maybe just email 📮) and
                                we make it for you.</p>
                        </div>
                    </div>
                    <div class="grid-cell-second-blue" span="2" style="background:#EEEFF2">
                        <div class="third-text-blue">
                            <h1 style="font-weight:900;font-size:40px;"><span
                                    style="font-size:55px">🤷‍♂️</span><br><br>But what is a section?</h1>
                            <p style="margin-top:30px;color: rgba(0, 0, 0, 0.7);">Sections are the components that make
                                up your whole store front. They can be customised in the Shopify theme editor. Shopify
                                themes include only a limited number of sections. With Online store 2.0 (sections
                                everywhere), you dont need to use page builders anymore. Sections can now be used as the
                                primary way to design all pages on your shop.</p>
                        </div>
                    </div>
                    <div class="grid-cell-full-blue" style="background:#C0E5FE">
                        <div style="text-align: center !important;">
                            <h1 style="font-weight:900;font-size:45px;">Find Your Section 🎉</h1>
                            <p style="margin-top:30px;color: rgba(0, 0, 0, 0.7);font-weight:900;font-size:20px;">Install
                                the free app and browse our wide variety of sections.</p>
                            <div style="margin-top:40px;">
                                <a class="bluelight-button" style="text-decoration:none;"
                                    href="https://apps.shopify.com/section-factory">Get Section Store 🛍</a>
                            </div>
                        </div>
                    </div>


                </div>
            </div>



        </div>
        <div id="shopify-section-template--16640583467257__e11746b6-f117-4877-bb92-0b517551e8ce"
            class="shopify-section">
            <style data-shopify>
                @font-face {
                    font-family: Montserrat;
                    font-weight: 500;
                    font-style: normal;
                    font-display: swap;
                    src: url("//section.store/cdn/fonts/montserrat/montserrat_n5.07ef3781d9c78c8b93c98419da7ad4fbeebb6635.woff2?h1=c2VjdGlvbi5zdG9yZQ&h2=c2VjdGlvbi1zdG9yZS1hcHAuYWNjb3VudC5teXNob3BpZnkuY29t&hmac=95120bd8da6a8ae9130672eab989f46564a0b51cfe96e7c05a30f13bfaf0e5cb") format("woff2"),
                        url("//section.store/cdn/fonts/montserrat/montserrat_n5.adf9b4bd8b0e4f55a0b203cdd84512667e0d5e4d.woff?h1=c2VjdGlvbi5zdG9yZQ&h2=c2VjdGlvbi1zdG9yZS1hcHAuYWNjb3VudC5teXNob3BpZnkuY29t&hmac=66a73a1530997bf4c8b80a545a8e30b62cc70a62bedbd85de12233ef1ec201d7") format("woff");
                }


                .back-top-wrapper-template--16640583467257__e11746b6-f117-4877-bb92-0b517551e8ce {
                    position: relative;
                }

                .back-top-template--16640583467257__e11746b6-f117-4877-bb92-0b517551e8ce {
                    padding: 5px;
                    position: fixed;
                    bottom: 20px;
                    background-color: #ffffff;
                    border-radius: 50%;
                    border: 0px solid #000000;
                    transition: all 0.3s ease-in-out;
                    cursor: pointer;
                    opacity: 0;
                    pointer-events: none;
                    z-index: -1;
                    transition: all 0.35s ease 0s;
                    transform: translateY(20px);
                }

                .back-top-template--16640583467257__e11746b6-f117-4877-bb92-0b517551e8ce.active {
                    transition: all 0.35s ease 0s;
                    opacity: 1;
                    pointer-events: all;
                    z-index: 2;
                    transform: translateY(0px);
                }

                .back-top-content-template--16640583467257__e11746b6-f117-4877-bb92-0b517551e8ce {
                    position: relative;
                    width: fit-content;
                    height: fit-content;
                    padding: 25px;
                }

                .back-top-image-template--16640583467257__e11746b6-f117-4877-bb92-0b517551e8ce {
                    position: relative;
                    z-index: 2;
                    border-radius: 0%;
                    width: 50px;
                    height: 50px;
                    background-color: #ffffff;
                }

                .back-top-image-template--16640583467257__e11746b6-f117-4877-bb92-0b517551e8ce img,
                .back-top-image-template--16640583467257__e11746b6-f117-4877-bb92-0b517551e8ce svg {
                    width: 100%;
                    height: 100%;
                    display: block;
                    object-fit: cover;
                    border-radius: 0%;
                }

                .back-top-text-template--16640583467257__e11746b6-f117-4877-bb92-0b517551e8ce {
                    height: 100%;
                    width: 100%;
                    position: absolute;
                    left: 0;
                    right: 0;
                    top: 0;
                    bottom: 0;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }

                .back-top-text_inner-template--16640583467257__e11746b6-f117-4877-bb92-0b517551e8ce {
                    height: 100%;
                    width: 100%;
                    transform-origin: center center;
                }

                #text-template--16640583467257__e11746b6-f117-4877-bb92-0b517551e8ce {
                    margin: 0px;
                    position: relative;
                }

                #text-template--16640583467257__e11746b6-f117-4877-bb92-0b517551e8ce span {
                    position: absolute;
                    transform-origin: 0 50px;
                    left: 50%;
                    color: #000000;
                    font-size: 12px;
                    font-weight: 700;
                    line-height: 100%;
                }

                #right-eye,
                #left-eye {
                    transform-origin: center;
                }

                @media(min-width: 1024px) {

                    .back-top-content-template--16640583467257__e11746b6-f117-4877-bb92-0b517551e8ce {
                        padding: 25px;
                    }

                    .back-top-image-template--16640583467257__e11746b6-f117-4877-bb92-0b517551e8ce {
                        width: 60px;
                        height: 60px;
                    }

                    #text-template--16640583467257__e11746b6-f117-4877-bb92-0b517551e8ce span {
                        transform-origin: 0 55px;
                        font-size: 10px;
                    }
                }
            </style>
            <style>
                #text-template--16640583467257__e11746b6-f117-4877-bb92-0b517551e8ce span {
                    font-family: Montserrat, sans-serif;
                    font-weight: 500;
                    font-style: normal;
                }
            </style>



            <style>
                .back-top-template--16640583467257__e11746b6-f117-4877-bb92-0b517551e8ce {
                    right: 20px;
                }
            </style>





            <style>
                .back-top-template--16640583467257__e11746b6-f117-4877-bb92-0b517551e8ce:hover .back-top-text_inner-template--16640583467257__e11746b6-f117-4877-bb92-0b517551e8ce {
                    animation: rotating_text 10s linear infinite;
                }

                @keyframes rotating_text {
                    from {
                        transform: rotate(0deg);
                    }

                    to {
                        transform: rotate(360deg);
                    }
                }
            </style>





            <style>
                .back-top-template--16640583467257__e11746b6-f117-4877-bb92-0b517551e8ce {
                    box-shadow: rgba(9, 30, 66, 0.25) 0px 4px 8px -2px, rgba(9, 30, 66, 0.08) 0px 0px 0px 1px;
                }
            </style>



            <style>
                .back-top-template--16640583467257__e11746b6-f117-4877-bb92-0b517551e8ce {
                    position: absolute;
                    bottom: unset;
                    top: -60px;
                }
            </style>


            <div class="back-top-wrapper-template--16640583467257__e11746b6-f117-4877-bb92-0b517551e8ce">
                <button class="back-top-template--16640583467257__e11746b6-f117-4877-bb92-0b517551e8ce active">
                    <div class="back-top-content-template--16640583467257__e11746b6-f117-4877-bb92-0b517551e8ce">
                        <div class="back-top-image-template--16640583467257__e11746b6-f117-4877-bb92-0b517551e8ce">

                            <svg width="79" height="73" viewBox="0 0 79 73" fill="none"
                                xmlns="http://www.w3.org/2000/svg" id="eyes">
                                <g filter="url(#filter0_d_350_47)">
                                    <path
                                        d="M10.1294 36.7103C10.1294 29.2549 11.9248 22.5582 14.7707 17.7621C17.6305 12.9424 21.4265 10.2166 25.4073 10.2166C29.3881 10.2166 33.184 12.9424 36.0438 17.7621C38.8897 22.5582 40.6851 29.2549 40.6851 36.7103C40.6851 44.1657 38.8897 50.8623 36.0438 55.6581C33.184 60.4781 29.388 63.2041 25.4072 63.2041C21.4265 63.2041 17.6305 60.4781 14.7707 55.6581C11.9248 50.8623 10.1294 44.1657 10.1294 36.7103Z"
                                        stroke="black" stroke-width="2.17752" />
                                    <mask id="mask0_350_47" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="9"
                                        y="9" width="33" height="56">
                                        <path
                                            d="M10.1294 36.7103C10.1294 29.2549 11.9248 22.5582 14.7707 17.7621C17.6305 12.9424 21.4265 10.2166 25.4073 10.2166C29.3881 10.2166 33.184 12.9424 36.0438 17.7621C38.8897 22.5582 40.6851 29.2549 40.6851 36.7103C40.6851 44.1657 38.8897 50.8623 36.0438 55.6581C33.184 60.4781 29.388 63.2041 25.4072 63.2041C21.4265 63.2041 17.6305 60.4781 14.7707 55.6581C11.9248 50.8623 10.1294 44.1657 10.1294 36.7103Z"
                                            fill="white" stroke="black" stroke-width="2.17752" />
                                    </mask>
                                    <g mask="url(#mask0_350_47)">
                                        <path id="left-eye"
                                            d="M20.4575 36.4566C20.4575 32.8168 22.9779 30.4545 25.4071 30.4545C27.8364 30.4545 30.3568 32.8168 30.3568 36.4566C30.3568 40.0964 27.8364 42.4587 25.4071 42.4587C22.9779 42.4587 20.4575 40.0964 20.4575 36.4566Z"
                                            fill="black" stroke="black" stroke-width="5.10075" />
                                    </g>
                                    <path
                                        d="M38.6606 36.7103C38.6606 29.2549 40.456 22.5582 43.3019 17.7621C46.1617 12.9424 49.9576 10.2166 53.9384 10.2166C57.9191 10.2166 61.7151 12.9424 64.5751 17.7621C67.4211 22.5582 69.2161 29.2549 69.2161 36.7103C69.2161 44.1657 67.4211 50.8623 64.5751 55.6581C61.7151 60.4781 57.9191 63.2041 53.9384 63.2041C49.9576 63.2041 46.1617 60.4781 43.3018 55.6581C40.456 50.8623 38.6606 44.1657 38.6606 36.7103Z"
                                        fill="white" stroke="black" stroke-width="2.17752" />
                                    <mask id="mask1_350_47" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="37"
                                        y="9" width="34" height="56">
                                        <path
                                            d="M38.6606 36.7103C38.6607 29.2549 40.456 22.5582 43.3019 17.7621C46.1617 12.9424 49.9577 10.2166 53.9385 10.2166C57.9191 10.2166 61.7151 12.9424 64.5751 17.7621C67.4211 22.5582 69.2161 29.2549 69.2161 36.7103C69.2161 44.1657 67.4211 50.8623 64.5751 55.6581C61.7151 60.4781 57.9191 63.2041 53.9385 63.2041C49.9577 63.2041 46.1617 60.4781 43.3019 55.6581C40.456 50.8623 38.6606 44.1657 38.6606 36.7103Z"
                                            fill="white" stroke="black" stroke-width="2.17752" />
                                    </mask>
                                    <g mask="url(#mask1_350_47)">
                                        <path id="right-eye"
                                            d="M48.9888 36.7102C48.9888 33.0704 51.5092 30.7081 53.9385 30.7081C56.3681 30.7081 58.8881 33.0704 58.8881 36.7102C58.8881 40.35 56.3681 42.7123 53.9385 42.7123C51.5092 42.7123 48.9888 40.35 48.9888 36.7102Z"
                                            fill="black" stroke="black" stroke-width="5.10075" />
                                    </g>
                                </g>
                                <defs>
                                    <filter id="filter0_d_350_47" x="0.344868" y="0.432148" width="78.6555"
                                        height="72.5564" filterUnits="userSpaceOnUse"
                                        color-interpolation-filters="sRGB">
                                        <feFlood flood-opacity="0" result="BackgroundImageFix" />
                                        <feColorMatrix in="SourceAlpha" type="matrix"
                                            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
                                        <feOffset />
                                        <feGaussianBlur stdDeviation="4.34783" />
                                        <feComposite in2="hardAlpha" operator="out" />
                                        <feColorMatrix type="matrix"
                                            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0" />
                                        <feBlend mode="normal" in2="BackgroundImageFix"
                                            result="effect1_dropShadow_350_47" />
                                        <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_350_47"
                                            result="shape" />
                                    </filter>
                                </defs>
                            </svg>

                        </div>


                        <div class="back-top-text-template--16640583467257__e11746b6-f117-4877-bb92-0b517551e8ce">
                            <div
                                class="back-top-text_inner-template--16640583467257__e11746b6-f117-4877-bb92-0b517551e8ce">
                                <p data-text="BACK TO TOP. BACK TO TOP. BACK TO TOP. "
                                    id="text-template--16640583467257__e11746b6-f117-4877-bb92-0b517551e8ce"></p>
                            </div>
                        </div>

                    </div>
                </button>
            </div>

            <script>
                function initBackToTop() {
                    var backToTopArrow = document.querySelector(
                        '.back-top-template--16640583467257__e11746b6-f117-4877-bb92-0b517551e8ce'
                    );
                    backToTopArrow.addEventListener('click', function () {
                        window.scrollTo({
                            top: 0,
                            behavior: 'smooth',
                        });
                    });


                    let text = document.getElementById('text-template--16640583467257__e11746b6-f117-4877-bb92-0b517551e8ce');
                    let str = text.getAttribute('data-text');
                    let coef = 360 / str.length;
                    let maxWidth = 0; // Переменная для хранения максимальной ширины

                    if (text) {
                        for (let i = 0; i < str.length; i++) {
                            let span = document.createElement('span');
                            span.innerHTML = str[i];
                            text.appendChild(span);

                            // Установите временный стиль для span, чтобы получить его ширину
                            span.style.transform = `rotate(${coef * i}deg)`;
                            let spanWidth = span.offsetWidth;

                            // Обновите maxWidth, если текущая ширина больше
                            maxWidth = Math.max(maxWidth, spanWidth);
                        }

                        // Пройдите по всем span и установите им максимальную ширину
                        for (let i = 0; i < text.children.length; i++) {
                            let span = text.children[i];
                            span.style.width = maxWidth + 'px';
                        }
                    }
                    var leftEye = document.getElementById('left-eye');
                    var rightEye = document.getElementById('right-eye');

                    document.addEventListener('mousemove', function (e) {
                        moveEye(leftEye, e.clientX, e.clientY);
                        moveEye(rightEye, e.clientX, e.clientY);
                    });

                    function moveEye(eye, mouseX, mouseY) {
                        var eyeRect = eye.getBoundingClientRect();
                        var eyeX = eyeRect.left + eyeRect.width / 2;
                        var eyeY = eyeRect.top + eyeRect.height / 2;

                        var deltaX = mouseX - eyeX;
                        var deltaY = mouseY - eyeY;

                        var userAgent = navigator.userAgent;
                        var isFirefox = userAgent.includes('Firefox');
                        var angle = Math.atan2(deltaY, deltaX);
                        var distance;
                        if (isFirefox) {
                            distance = Math.min(eyeRect.width, eyeRect.height) / (1 * 3.0);
                        } else {
                            distance = Math.min(eyeRect.width, eyeRect.height) / (1 * 0.6);
                        }

                        var distance2 = Math.sqrt(deltaX ** 2 + deltaY ** 2);
                        var scaleMultiplier = 0.0006;
                        var scaleY = 1 + distance2 * scaleMultiplier;


                        var newX = Math.cos(angle) * distance;
                        var newY = Math.sin(angle) * distance;

                        eye.style.transform = 'translate(' + newX + 'px, ' + newY + 'px) scaleY(' + scaleY + ')';
                    }
                }
                document.addEventListener('DOMContentLoaded', initBackToTop);
                if (Shopify.designMode) {
                    document.addEventListener('shopify:section:unload', initBackToTop);
                    document.addEventListener('shopify:section:load', initBackToTop);
                }
            </script>

        </div>
    </main>


    <div id="shopify-section-footer" class="shopify-section">
        <link href="//section.store/cdn/shop/t/1/assets/section-footer.css?v=83777583229853969691663702907"
            rel="stylesheet" type="text/css" media="all" />
        <link rel="stylesheet"
            href="//section.store/cdn/shop/t/1/assets/component-newsletter.css?v=103472482056003053551663702901"
            media="print" onload="this.media='all'">
        <link rel="stylesheet"
            href="//section.store/cdn/shop/t/1/assets/component-list-menu.css?v=151968516119678728991663702890"
            media="print" onload="this.media='all'">
        <link rel="stylesheet"
            href="//section.store/cdn/shop/t/1/assets/component-list-payment.css?v=69253961410771838501663702883"
            media="print" onload="this.media='all'">
        <link rel="stylesheet"
            href="//section.store/cdn/shop/t/1/assets/component-list-social.css?v=52211663153726659061663702872"
            media="print" onload="this.media='all'">
        <link rel="stylesheet"
            href="//section.store/cdn/shop/t/1/assets/component-rte.css?v=69919436638515329781663702902" media="print"
            onload="this.media='all'">
        <link rel="stylesheet" href="//section.store/cdn/shop/t/1/assets/disclosure.css?v=646595190999601341663702883"
            media="print" onload="this.media='all'">

        <noscript>
            <link href="//section.store/cdn/shop/t/1/assets/component-newsletter.css?v=103472482056003053551663702901"
                rel="stylesheet" type="text/css" media="all" />
        </noscript>
        <noscript>
            <link href="//section.store/cdn/shop/t/1/assets/component-list-menu.css?v=151968516119678728991663702890"
                rel="stylesheet" type="text/css" media="all" />
        </noscript>
        <noscript>
            <link href="//section.store/cdn/shop/t/1/assets/component-list-payment.css?v=69253961410771838501663702883"
                rel="stylesheet" type="text/css" media="all" />
        </noscript>
        <noscript>
            <link href="//section.store/cdn/shop/t/1/assets/component-list-social.css?v=52211663153726659061663702872"
                rel="stylesheet" type="text/css" media="all" />
        </noscript>
        <noscript>
            <link href="//section.store/cdn/shop/t/1/assets/component-rte.css?v=69919436638515329781663702902"
                rel="stylesheet" type="text/css" media="all" />
        </noscript>
        <noscript>
            <link href="//section.store/cdn/shop/t/1/assets/disclosure.css?v=646595190999601341663702883"
                rel="stylesheet" type="text/css" media="all" />
        </noscript>
        <style data-shopify>
            .footer {
                margin-top: 0px;
            }

            .section-footer-padding {
                padding-top: 36px;
                padding-bottom: 27px;
            }

            @media screen and (min-width: 750px) {
                .footer {
                    margin-top: 0px;
                }

                .section-footer-padding {
                    padding-top: 48px;
                    padding-bottom: 36px;
                }
            }
        </style>
        <footer class="footer color-background-1 gradient section-footer-padding">
            <div class="footer__content-top page-width">
                <div class="footer-block--newsletter">
                    <ul class="footer__list-social list-unstyled list-social" role="list">
                        <li class="list-social__item">
                            <a href="https://www.tiktok.com/@section.store" class="link list-social__link"><svg
                                    aria-hidden="true" focusable="false" role="presentation" class="icon icon-tiktok"
                                    width="16" height="18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M8.02 0H11s-.17 3.82 4.13 4.1v2.95s-2.3.14-4.13-1.26l.03 6.1a5.52 5.52 0 11-5.51-5.52h.77V9.4a2.5 2.5 0 101.76 2.4L8.02 0z"
                                        fill="currentColor">
                                </svg>
                                <span class="visually-hidden">TikTok</span>
                            </a>
                        </li>
                        <li class="list-social__item">
                            <a href="https://www.youtube.com/channel/UCwL7vHNBc_xFZVQcH84MDvQ"
                                class="link list-social__link"><svg aria-hidden="true" focusable="false"
                                    role="presentation" class="icon icon-youtube" viewBox="0 0 100 70">
                                    <path
                                        d="M98 11c2 7.7 2 24 2 24s0 16.3-2 24a12.5 12.5 0 01-9 9c-7.7 2-39 2-39 2s-31.3 0-39-2a12.5 12.5 0 01-9-9c-2-7.7-2-24-2-24s0-16.3 2-24c1.2-4.4 4.6-7.8 9-9 7.7-2 39-2 39-2s31.3 0 39 2c4.4 1.2 7.8 4.6 9 9zM40 50l26-15-26-15v30z"
                                        fill="currentColor">
                                </svg>
                                <span class="visually-hidden">YouTube</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="footer__content-bottom">
                <div class="footer__content-bottom-wrapper page-width">
                    <div class="footer__column footer__localization isolate"></div>
                    <div class="footer__column footer__column--info">
                        <div class="footer__copyright caption">
                            <small class="copyright__content">&copy; 2025, <a href="/" title="">Section
                                    Store</a></small>
                            <small class="copyright__content"><a target="_blank" rel="nofollow"
                                    href="https://www.shopify.com?utm_campaign=poweredby&amp;utm_medium=shopify&amp;utm_source=onlinestore">Powered
                                    by Shopify</a></small>
                        </div>
                    </div>
                </div>
            </div>
        </footer>




    </div>


    <ul hidden>
        <li id="a11y-refresh-page-message">Choosing a selection results in a full page refresh.</li>
        <li id="a11y-new-window-message">Opens in a new window.</li>
    </ul>

    <script>
        window.shopUrl = 'https://section.store';
        window.routes = {
            cart_add_url: '/cart/add',
            cart_change_url: '/cart/change',
            cart_update_url: '/cart/update',
            cart_url: '/cart',
            predictive_search_url: '/search/suggest'
        };

        window.cartStrings = {
            error: `There was an error while updating your cart. Please try again.`,
            quantityError: `You can only add [quantity] of this item to your cart.`
        }

        window.variantStrings = {
            addToCart: `Add to cart`,
            soldOut: `Sold out`,
            unavailable: `Unavailable`,
        }

        window.accessibilityStrings = {
            imageAvailable: `Image [index] is now available in gallery view`,
            shareSuccess: `Link copied to clipboard`,
            pauseSlideshow: `Pause slideshow`,
            playSlideshow: `Play slideshow`,
        }
    </script>
    <script src="//section.store/cdn/shop/t/1/assets/predictive-search.js?v=31278710863581584031663702888"
        defer="defer"></script>
</body>

</html>