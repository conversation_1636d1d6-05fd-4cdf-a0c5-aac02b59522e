<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>S<PERSON> vs Handlebars vs Liquid 语法对比</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 20px;
            background-color: #f9f9f9;
        }
        h1 {
            text-align: center;
            color: #2c3e50;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 25px 0;
            font-size: 0.9em;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.15);
            background-color: #fff;
        }
        thead tr {
            background-color: #009879;
            color: #ffffff;
            text-align: left;
        }
        th, td {
            padding: 12px 15px;
            border: 1px solid #dddddd;
            text-align: left;
            vertical-align: middle;
        }
        tbody tr {
            border-bottom: 1px solid #dddddd;
        }
        tbody tr:nth-of-type(even) {
            background-color: #f3f3f3;
        }
        tbody tr:last-of-type {
            border-bottom: 2px solid #009879;
        }
        td:first-child {
            font-weight: bold;
            background-color: #f2f2f2;
        }
        code {
            background-color: #e8e8e8;
            padding: 2px 5px;
            border-radius: 3px;
            font-family: "Courier New", Courier, monospace;
            white-space: nowrap;
        }
    </style>
</head>
<body>

    <h1>综合语法对比表：Sline vs Handlebars vs Liquid</h1>

    <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 20px 0; border-radius: 5px;">
        <strong>⚠️ 重要说明：</strong>
        <p style="margin: 5px 0 0 0;">本对比表基于官方文档编制，已验证的语法包括：基础语法、字符串定义、作用域访问、条件判断、注释和过滤器。部分 Sline 高级功能（如 set、capture、switch/case、for 等）在当前官方文档中缺乏详细说明，标记为"待验证"，请以 <a href="https://developer.shopline.com/zh-hans-cn/docs/sline/" target="_blank">SHOPLINE 官方文档</a> 为准。</p>
    </div>

    <table>
        <thead>
            <tr>
                <th>特性分类</th>
                <th>功能点</th>
                <th>Sline 语法</th>
                <th>Handlebars 语法</th>
                <th>Liquid 语法</th>
                <th>备注</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td rowspan="4"><strong>基础语法</strong></td>
                <td>标准输出</td>
                <td><code>{{ expression }}</code></td>
                <td><code>{{ expression }}</code></td>
                <td><code>{{ expression }}</code></td>
                <td>三者通用，默认进行 HTML 转义。</td>
            </tr>
            <tr>
                <td>原始输出</td>
                <td><code>{{{ expression }}}</code></td>
                <td><code>{{{ expression }}}</code></td>
                <td><code>{% raw %}...{% endraw %}</code></td>
                <td>Sline 继承自 Handlebars。Liquid 使用标签处理代码块。</td>
            </tr>
            <tr>
                <td>逻辑/标签分隔符</td>
                <td><code>{{#tag}}...{{/tag}}</code></td>
                <td><code>{{#helper}}...{{/helper}}</code></td>
                <td><code>{% tag %}...{% endtag %}</code></td>
                <td>Sline 的核心结构基于 Handlebars。</td>
            </tr>
            <tr>
                <td>注释</td>
                <td><code>{{!-- comment --}}</code><br><code>{{#comment}}...{{/comment}}</code></td>
                <td><code>{{!-- comment --}}</code></td>
                <td><code>{% comment %}...{% endcomment %}</code></td>
                <td>Sline 支持两种注释语法：行内注释和块级注释。Liquid 也支持行内注释 <code>{% #... %}</code>。</td>
            </tr>
            <tr>
                <td rowspan="4"><strong>数据处理</strong></td>
                <td>变量赋值</td>
                <td><code>{{#set 'var' 'val'}}</code> <em>(待验证)</em></td>
                <td>需自定义助手</td>
                <td><code>{% assign var = 'val' %}</code></td>
                <td>Sline 为弥补 Handlebars 的不足而引入了此关键功能。</td>
            </tr>
            <tr>
                <td>捕获块为变量</td>
                <td><code>{{#capture 'var'}}...{{/capture}}</code> <em>(待验证)</em></td>
                <td>需自定义助手</td>
                <td><code>{% capture var %}...{% endcapture %}</code></td>
                <td>Sline 提供了与 Liquid 同等强大的块捕获能力。</td>
            </tr>
            <tr>
                <td>字符串定义</td>
                <td><code>"str"</code>, <code>'a'</code>, <code>`str`</code></td>
                <td><code>"str"</code>, <code>'str'</code></td>
                <td><code>"str"</code>, <code>'str'</code></td>
                <td>Sline 对单引号和反引号有特殊用法：单引号表示单个字符，反引号为原始字符串。</td>
            </tr>
            <tr>
                <td>数组索引访问</td>
                <td><code>{{ array[index] }}</code></td>
                <td><code>{{ array.[index] }}</code></td>
                <td><code>{{ array[index] }}</code></td>
                <td>Sline 采用了更符合 JS 习惯的方括号语法，Handlebars 使用点加方括号的形式。</td>
            </tr>
            <tr>
                <td rowspan="2"><strong>作用域</strong></td>
                <td>当前上下文 (<code>this</code>)</td>
                <td><code>{{ title }}</code> (隐式)</td>
                <td><code>{{ this.title }}</code> (显式)</td>
                <td>隐式上下文，直接访问属性</td>
                <td>Sline/Liquid 语法更简洁，Handlebars 更明确。</td>
            </tr>
            <tr>
                <td>访问父级作用域</td>
                <td>不支持 <code>../</code></td>
                <td><code>{{../property }}</code></td>
                <td>不支持，需通过 <code>render</code> 传参</td>
                <td>Sline 为实现更好的封装性而禁用了此功能。</td>
            </tr>
            <tr>
                <td rowspan="3"><strong>控制流</strong></td>
                <td>条件判断</td>
                <td><code>{{#if}}...{{#else/}}...{{/if}}</code></td>
                <td><code>{{#if}}...{{else}}...{{/if}}</code></td>
                <td><code>{% if %}...{% elsif %}...{% endif %}</code></td>
                <td>Sline 使用自闭合的 `{{#else/}}` 标签。Liquid 使用 `elsif`。</td>
            </tr>
            <tr>
                <td>反向条件</td>
                <td><strong>不支持</strong></td>
                <td><code>{{#unless}}...{{/unless}}</code></td>
                <td><code>{% unless %}...{% endunless %}</code></td>
                <td>Sline 不支持 unless 标签，需要使用 if 配合逻辑运算符实现反向条件。</td>
            </tr>
            <tr>
                <td>Switch/Case</td>
                <td><code>{{#switch}}...{{#case}}...{{/switch}}</code> <em>(待验证)</em></td>
                <td>需自定义助手</td>
                <td><code>{% case %}...{% when %}...{% endcase %}</code></td>
                <td>Sline 引入了 Liquid 的 `case` 语句以增强逻辑处理。</td>
            </tr>
            <tr>
                <td rowspan="4"><strong>迭代</strong></td>
                <td>For 循环</td>
                <td><code>{{#for array as |item|}}...{{/for}}</code> <em>(待验证)</em></td>
                <td><code>{{#each array as |item|}}...{{/each}}</code></td>
                <td><code>{% for item in array %}...{% endfor %}</code></td>
                <td>Sline 的 `for` 助手是 Handlebars 语法和 Liquid 功能的混合体。</td>
            </tr>
            <tr>
                <td>循环元数据</td>
                <td><code>forloop.index</code>, <code>forloop.first</code> <em>(待验证)</em></td>
                <td><code>@index</code>, <code>@first</code></td>
                <td><code>forloop.index</code>, <code>forloop.first</code></td>
                <td>Sline 采用了 Liquid 的命名约定，更具可读性。</td>
            </tr>
            <tr>
                <td>访问父级循环</td>
                <td><code>forloop.parentloop</code> <em>(待验证)</em></td>
                <td>不支持</td>
                <td><code>forloop.parentloop</code></td>
                <td>Sline 与 Liquid 保持一致，以处理嵌套循环。</td>
            </tr>
            <tr>
                <td>循环参数</td>
                <td><code>limit</code>, <code>offset</code>, <code>reversed</code> <em>(待验证)</em></td>
                <td>需自定义助手</td>
                <td><code>limit</code>, <code>offset</code>, <code>reversed</code></td>
                <td>Sline 实现了 Liquid 的强大循环修饰符。</td>
            </tr>
            <tr>
                <td rowspan="2"><strong>模块化</strong></td>
                <td>引入代码片段</td>
                <td><code>{{#snippet 'name'}}</code> <em>(待验证)</em></td>
                <td><code>{{> partialName }}</code></td>
                <td><code>{% render 'snippet-name' %}</code></td>
                <td>Sline 使用 `snippet` 或 `component` 助手。</td>
            </tr>
            <tr>
                <td>向片段传递数据</td>
                <td><code>{{#snippet 'name' key=val}}</code> <em>(待验证)</em></td>
                <td><code>{{> partialName context}}</code></td>
                <td><code>{% render 'name', key: val %}</code></td>
                <td>语法各异，但都支持显式数据传递。</td>
            </tr>
            <tr>
                <td rowspan="2"><strong>数据转换</strong></td>
                <td>过滤器/助手语法</td>
                <td><code>{{ value | filter:arg }}</code></td>
                <td><code>{{ helper value arg }}</code></td>
                <td><code>{{ value | filter: arg }}</code></td>
                <td>Sline 采纳了 Liquid 的管道语法，是其关键设计决策。</td>
            </tr>
            <tr>
                <td>链式调用</td>
                <td><code>{{ val | f1 | f2 }}</code></td>
                <td><code>{{ f2 (f1 val) }}</code></td>
                <td><code>{{ val | f1 | f2 }}</code></td>
                <td>管道语法的可读性远优于嵌套函数调用。</td>
            </tr>
            <tr>
                <td><strong>扩展能力</strong></td>
                <td>核心扩展方式</td>
                <td>标签 (Tags), 过滤器 (Filters)</td>
                <td>助手 (Helpers)</td>
                <td>标签 (Tags), 过滤器 (Filters)</td>
                <td>Sline 融合了 Handlebars 的标签结构和 Liquid 的过滤器语法。</td>
            </tr>
        </tbody>
    </table>

    <!-- 引用来源模块 -->
    <div style="margin-top: 40px; padding: 20px; background-color: #f8f9fa; border-left: 4px solid #007bff; border-radius: 5px;">
        <h2 style="color: #007bff; margin-top: 0; font-size: 1.2em;">📚 引用来源</h2>

        <h3 style="color: #495057; font-size: 1.1em; margin-bottom: 10px;">Sline 官方文档</h3>
        <ul style="margin-bottom: 20px;">
            <li><a href="https://developer.shopline.com/zh-hans-cn/docs/sline/sline-overview?version=v20251201" target="_blank">Sline 概述</a> - SHOPLINE 开发者文档</li>
            <li><a href="https://developer.shopline.com/zh-hans-cn/docs/sline/basics?version=v20251201" target="_blank">Sline 基础知识</a> - SHOPLINE 开发者文档</li>
            <li><a href="https://developer.shopline.com/zh-hans-cn/docs/sline/syntax-differences-sline-vs-handlebars?version=v20251201" target="_blank">Sline & Handlebars 语法差异对比</a> - SHOPLINE 开发者文档</li>
        </ul>

        <h3 style="color: #495057; font-size: 1.1em; margin-bottom: 10px;">Handlebars 官方文档</h3>
        <ul style="margin-bottom: 20px;">
            <li><a href="https://handlebarsjs.com/zh/guide/" target="_blank">Handlebars 中文指南</a> - Handlebars 官方文档</li>
            <li><a href="https://handlebarsjs.com/zh/guide/expressions.html" target="_blank">表达式</a> - Handlebars 官方文档</li>
            <li><a href="https://handlebarsjs.com/zh/guide/builtin-helpers.html" target="_blank">内置助手代码</a> - Handlebars 官方文档</li>
        </ul>

        <h3 style="color: #495057; font-size: 1.1em; margin-bottom: 10px;">Liquid 官方文档</h3>
        <ul style="margin-bottom: 20px;">
            <li><a href="https://shopify.github.io/liquid/" target="_blank">Liquid 模板语言</a> - Shopify 官方文档</li>
            <li><a href="https://shopify.github.io/liquid/basics/introduction/" target="_blank">Liquid 基础介绍</a> - Shopify 官方文档</li>
            <li><a href="https://shopify.github.io/liquid/tags/template/" target="_blank">Liquid 模板标签</a> - Shopify 官方文档</li>
        </ul>

        <div style="margin-top: 20px; padding: 15px; background-color: #e9ecef; border-radius: 3px;">
            <p style="margin: 0; font-size: 0.9em; color: #6c757d;">
                <strong>编制说明：</strong>本对比表基于上述官方文档编制，最后更新时间：2025年1月28日。
                部分 Sline 高级功能因官方文档尚未完善而标记为"待验证"，建议开发者以最新官方文档为准。
            </p>
        </div>
    </div>

</body>
</html>