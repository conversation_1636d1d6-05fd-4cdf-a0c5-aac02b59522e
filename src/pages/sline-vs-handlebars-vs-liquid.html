<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>S<PERSON> vs Handlebars vs Liquid 语法对比（已审核）</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 20px;
            background-color: #f9f9f9;
        }
        h1 {
            text-align: center;
            color: #2c3e50;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 25px 0;
            font-size: 0.9em;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.15);
            background-color: #fff;
        }
        thead tr {
            background-color: #009879;
            color: #ffffff;
            text-align: left;
        }
        th, td {
            padding: 12px 15px;
            border: 1px solid #dddddd;
            text-align: left;
            vertical-align: middle;
        }
        tbody tr {
            border-bottom: 1px solid #dddddd;
        }
        tbody tr:nth-of-type(even) {
            background-color: #f3f3f3;
        }
        tbody tr:last-of-type {
            border-bottom: 2px solid #009879;
        }
        td:first-child {
            font-weight: bold;
            background-color: #f2f2f2;
        }
        code {
            background-color: #e8e8e8;
            padding: 2px 5px;
            border-radius: 3px;
            font-family: "Courier New", Courier, monospace;
            white-space: nowrap;
        }
    </style>
</head>
<body>

    <h1>综合语法对比表：Sline vs Handlebars vs Liquid (已审核)</h1>

    <table>
        <thead>
            <tr>
                <th>特性分类</th>
                <th>功能点</th>
                <th>Sline 语法</th>
                <th>Handlebars 语法</th>
                <th>Liquid 语法</th>
                <th>备注</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td rowspan="4"><strong>基础语法</strong></td>
                <td>标准输出</td>
                <td><code>{{ expression }}</code></td>
                <td><code>{{ expression }}</code></td>
                <td><code>{{ expression }}</code></td>
                <td>三者通用，默认进行 HTML 转义 [1, 2, 3]。</td>
            </tr>
            <tr>
                <td>原始输出</td>
                <td><code>{{{ expression }}}</code></td>
                <td><code>{{{ expression }}}</code></td>
                <td><code>{% raw %}...{% endraw %}</code></td>
                <td>Sline 继承自 Handlebars [1, 3]。Liquid 使用标签处理代码块 [2]。</td>
            </tr>
            <tr>
                <td>逻辑/标签分隔符</td>
                <td><code>{{#tag}}...{{/tag}}</code></td>
                <td><code>{{#helper}}...{{/helper}}</code></td>
                <td><code>{% tag %}...{% endtag %}</code></td>
                <td>Sline 的核心结构基于 Handlebars [4, 3]。</td>
            </tr>
            <tr>
                <td>注释</td>
                <td><code>{{!-- comment --}}</code></td>
                <td><code>{{!-- comment --}}</code></td>
                <td><code>{% comment %}...{% endcomment %}</code></td>
                <td>Sline 再次遵循 Handlebars 的语法 [4]。Liquid 也支持行内注释 <code>{% #... %}</code> [2]。</td>
            </tr>
            <tr>
                <td rowspan="4"><strong>数据处理</strong></td>
                <td>变量赋值</td>
                <td><code>{{#set 'var' 'val'}}</code></td>
                <td>需自定义助手</td>
                <td><code>{% assign var = 'val' %}</code></td>
                <td>Sline 为弥补 Handlebars 的不足而引入了此关键功能 [5, 2]。</td>
            </tr>
            <tr>
                <td>捕获块为变量</td>
                <td><code>{{#capture 'var'}}...{{/capture}}</code></td>
                <td>需自定义助手</td>
                <td><code>{% capture var %}...{% endcapture %}</code></td>
                <td>Sline 提供了与 Liquid 同等强大的块捕获能力 [5, 2]。</td>
            </tr>
            <tr>
                <td>字符串定义</td>
                <td><code>"str"</code>, <code>'a'</code>, <code>`str`</code></td>
                <td><code>"str"</code>, <code>'str'</code></td>
                <td><code>"str"</code>, <code>'str'</code></td>
                <td>Sline 对单引号和反引号有特殊用法 [4, 6]。</td>
            </tr>
            <tr>
                <td>数组索引访问</td>
                <td><code>{{ array }}</code></td>
                <td><code>{{ array. }}</code></td>
                <td><code>{{ array }}</code></td>
                <td>Sline 采用了更符合 JS 习惯的语法，与 Handlebars 的 `.` 形式不同 [4]。</td>
            </tr>
            <tr>
                <td rowspan="2"><strong>作用域</strong></td>
                <td>当前上下文 (<code>this</code>)</td>
                <td><code>{{ title }}</code> (隐式)</td>
                <td><code>{{ this.title }}</code> (显式)</td>
                <td>隐式上下文，直接访问属性</td>
                <td>Sline/Liquid 语法更简洁，Handlebars 更明确 [4, 2]。</td>
            </tr>
            <tr>
                <td>访问父级作用域</td>
                <td>不支持 <code>../</code></td>
                <td><code>{{../property }}</code></td>
                <td>不支持，需通过 <code>render</code> 传参</td>
                <td>Sline 为实现更好的封装性而禁用了此功能 [4, 2]。</td>
            </tr>
            <tr>
                <td rowspan="3"><strong>控制流</strong></td>
                <td>条件判断</td>
                <td><code>{{#if}}...{{else if}}...{{/if}}</code></td>
                <td><code>{{#if}}...{{else}}...{{/if}}</code></td>
                <td><code>{% if %}...{% elsif %}...{% endif %}</code></td>
                <td>Sline 扩展了 Handlebars，支持 `else if`。Liquid 使用 `elsif` [2, 3]。</td>
            </tr>
            <tr>
                <td>反向条件</td>
                <td><code>{{#unless}}...{{/unless}}</code></td>
                <td><code>{{#unless}}...{{/unless}}</code></td>
                <td><code>{% unless %}...{% endunless %}</code></td>
                <td>三者均支持反向条件判断 [2, 3]。</td>
            </tr>
            <tr>
                <td>Switch/Case</td>
                <td><code>{{#switch}}...{{#case}}...{{/switch}}</code></td>
                <td>需自定义助手</td>
                <td><code>{% case %}...{% when %}...{% endcase %}</code></td>
                <td>Sline 引入了 Liquid 的 `case` 语句以增强逻辑处理 [5, 2]。</td>
            </tr>
            <tr>
                <td rowspan="4"><strong>迭代</strong></td>
                <td>For 循环</td>
                <td><code>{{#for array as |item|}}...{{/for}}</code></td>
                <td><code>{{#each array as |item|}}...{{/each}}</code></td>
                <td><code>{% for item in array %}...{% endfor %}</code></td>
                <td>Sline 的 `for` 助手是 Handlebars 语法和 Liquid 功能的混合体 。</td>
            </tr>
            <tr>
                <td>循环元数据</td>
                <td><code>forloop.index</code>, <code>forloop.first</code></td>
                <td><code>@index</code>, <code>@first</code></td>
                <td><code>forloop.index</code>, <code>forloop.first</code></td>
                <td>Sline 采用了 Liquid 的命名约定，更具可读性 [2, 7, 8]。</td>
            </tr>
            <tr>
                <td>访问父级循环</td>
                <td><code>forloop.parentloop</code></td>
                <td>不支持</td>
                <td><code>forloop.parentloop</code></td>
                <td>Sline 再次与 Liquid 保持一致，以处理嵌套循环 。</td>
            </tr>
            <tr>
                <td>循环参数</td>
                <td><code>limit</code>, <code>offset</code>, <code>reversed</code></td>
                <td>需自定义助手</td>
                <td><code>limit</code>, <code>offset</code>, <code>reversed</code></td>
                <td>Sline 实现了 Liquid 的强大循环修饰符 。</td>
            </tr>
            <tr>
                <td rowspan="2"><strong>模块化</strong></td>
                <td>引入代码片段</td>
                <td><code>{{#snippet 'name'}}</code></td>
                <td><code>{{> partialName }}</code></td>
                <td><code>{% render 'snippet-name' %}</code></td>
                <td>Sline 使用 `snippet` 或 `component` 助手 [2, 9, 3]。</td>
            </tr>
            <tr>
                <td>向片段传递数据</td>
                <td><code>{{#snippet 'name' key=val}}</code></td>
                <td><code>{{> partialName context}}</code></td>
                <td><code>{% render 'name', key: val %}</code></td>
                <td>语法各异，但都支持显式数据传递 [2, 3]。</td>
            </tr>
            <tr>
                <td rowspan="2"><strong>数据转换</strong></td>
                <td>过滤器/助手语法</td>
                <td><code>{{ value | filter:arg }}</code></td>
                <td><code>{{ helper value arg }}</code></td>
                <td><code>{{ value | filter: arg }}</code></td>
                <td>Sline 采纳了 Liquid 的管道语法，是其关键设计决策 [4, 2, 3]。</td>
            </tr>
            <tr>
                <td>链式调用</td>
                <td><code>{{ val | f1 | f2 }}</code></td>
                <td><code>{{ f2 (f1 val) }}</code></td>
                <td><code>{{ val | f1 | f2 }}</code></td>
                <td>管道语法的可读性远优于嵌套函数调用 [4, 2, 3]。</td>
            </tr>
            <tr>
                <td><strong>扩展能力</strong></td>
                <td>核心扩展方式</td>
                <td>标签 (Tags), 过滤器 (Filters)</td>
                <td>助手 (Helpers)</td>
                <td>标签 (Tags), 过滤器 (Filters)</td>
                <td>Sline 融合了 Handlebars 的标签结构和 Liquid 的过滤器语法 [4, 1, 2, 3]。</td>
            </tr>
        </tbody>
    </table>

</body>
</html>