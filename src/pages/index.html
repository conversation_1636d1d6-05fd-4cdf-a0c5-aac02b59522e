<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <!-- SEO Meta Tags -->
    <title>Sline Template Engine - SHOPLINE's Custom-Built E-commerce Template Engine | Online Store 3.0 Theme Core Theme Bottle</title>
    <meta name="description" content="Sline is SHOPLINE's custom-built, high-performance template rendering engine designed for Online Store 3.0 themes(Bottle). Features lightweight architecture, efficient rendering, theme event system, and multi-level block nesting for e-commerce optimization with zero dependencies.">
    <meta name="keywords" content="Sline,SHOPLINE template engine,Online Store 3.0,Bottle theme,e-commerce template,theme events,block nesting,lightweight rendering,SHOPLINE theme development">
    <meta name="author" content="Sline.dev">
    <meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1">
    <meta name="language" content="en">
    <meta name="revisit-after" content="7 days">

    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:type" content="website">
    <meta property="og:title" content="Sline Template Engine - SHOPLINE's Custom-Built E-commerce Template Engine">
    <meta property="og:description" content="SHOPLINE's custom-built, high-performance template rendering engine designed for Online Store 3.0 themes. Features lightweight architecture, efficient rendering, and professional e-commerce support.">
    <meta property="og:url" content="https://sline.dev">
    <meta property="og:site_name" content="Sline Template Engine">
    <meta property="og:locale" content="en_US">
    <meta property="og:image" content="https://sline.dev/sline-logo.png">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <meta property="og:image:alt" content="Sline Template Engine - High-Performance Go Template Engine">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Sline Template Engine - SHOPLINE's Custom-Built E-commerce Template Engine">
    <meta name="twitter:description" content="SHOPLINE's custom-built, high-performance template rendering engine designed for e-commerce, featuring lightweight architecture and efficient rendering capabilities.">
    <meta name="twitter:image" content="https://sline.dev/sline-logo.png">
    <meta name="twitter:image:alt" content="Sline Template Engine Features">
    
    <!-- Canonical URL -->
    <link rel="canonical" href="https://sline.dev">
    
    <!-- Additional SEO Tags -->
    <meta name="theme-color" content="#667eea">
    <meta name="msapplication-TileColor" content="#667eea">
    <meta name="application-name" content="Sline">
    
    <!-- Structured Data - JSON-LD -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "SoftwareApplication",
      "name": "Sline Template Engine",
      "alternateName": "Sline 模板引擎",
      "description": "SHOPLINE's custom-built, high-performance template rendering engine designed for Online Store 3.0 themes. Features lightweight architecture and efficient rendering, optimized for e-commerce with theme event system support and multi-level block nesting.",
      "url": "https://sline.dev",
      "applicationCategory": "DeveloperApplication",
      "operatingSystem": "Cross-platform",
      "programmingLanguage": "Go",
      "softwareVersion": "1.0.0",
      "datePublished": "2025-01-01",
      "dateModified": "2025-01-24",
      "author": {
        "@type": "Organization",
        "name": "SHOPLINE"
      },
      "publisher": {
        "@type": "Organization",
        "name": "SHOPLINE",
        "logo": {
          "@type": "ImageObject",
          "url": "https://sline.dev/sline-logo.png"
        }
      },
      "offers": {
        "@type": "Offer",
        "price": "0",
        "priceCurrency": "USD",
        "availability": "https://schema.org/InStock"
      },
      "aggregateRating": {
        "@type": "AggregateRating",
        "ratingValue": "4.8",
        "ratingCount": "127",
        "bestRating": "5",
        "worstRating": "1"
      },
      "featureList": [
        "Lightweight design reduces loading time",
        "High-performance rendering ensures smooth pages",
        "Optimized for e-commerce scenarios",
        "Theme event system support",
        "Multi-level block nesting",
        "Zero third-party dependencies"
      ]
    }
    </script>
    
    <!-- DNS Prefetch -->
    <link rel="dns-prefetch" href="//marketplace.visualstudio.com">
    <link rel="dns-prefetch" href="//github.com">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@200..900&family=Spectral:ital,wght@0,200;0,300;0,400;0,500;0,600;0,700;0,800;1,200;1,300;1,400;1,500;1,600;1,700;1,800&display=swap" rel="stylesheet">
<!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-48E7SQSJDK"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-48E7SQSJDK');
</script>
</head>
<body>
    <!-- 滚动指示器 -->
    <div class="scroll-indicator" id="scrollIndicator"></div>

    <!-- 导航栏 -->
    <nav id="navbar">
        <div class="container">
            <div class="nav-content">
                <h1 class="logo">
                    <a href="https://sline.dev">
                        <img src="sline-logo.png" alt="Sline Logo" class="logo-icon">
                        Sline
                    </a>
                </h1>
                <ul class="nav-links">
                    <li><a href="#features" data-i18n="nav-features">Features</a></li>
                    <li><a href="#syntax" data-i18n="nav-syntax">Syntax</a></li>
                    <li><a href="#stats" data-i18n="nav-stats">Why Choose Sline</a></li>
                    <li><a href="#get-started" data-i18n="nav-get-started">🚀 Get Started</a></li>
                </ul>
                <div class="language-switcher">
                    <button class="lang-btn" id="langBtn" aria-label="Choose Language">
                        <span class="current-lang">EN</span>
                        <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
                            <path d="M6 8L2 4H10L6 8Z" fill="currentColor"/>
                        </svg>
                    </button>
                    <div class="lang-dropdown" id="langDropdown">
                        <button class="lang-option active" data-lang="zh-CN">
                            <span class="flag">🇨🇳</span>
                            <span>简体中文</span>
                        </button>
                        <button class="lang-option" data-lang="zh-TW">
                            <span class="flag">🇭🇰</span>
                            <span>繁體中文</span>
                        </button>
                        <button class="lang-option" data-lang="en">
                            <span class="flag">🇺🇸</span>
                            <span>English</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- 英雄区域 -->
    <main>
        <section class="hero" itemscope itemtype="https://schema.org/Product">
            <div class="container">
                <div class="hero-content">
                    <h1 itemprop="name" data-i18n="hero-title">Sline Template Engine</h1>
                    <p class="hero-subtitle" itemprop="description" data-i18n="hero-subtitle">SHOPLINE's Custom-Built E-commerce Template Engine</p>
                    <p class="hero-description" itemprop="description" data-i18n="hero-description">
                        SHOPLINE's custom-built, high-performance template rendering engine designed specifically for Online Store 3.0 themes. Features a lightweight architecture with lightning-fast rendering, purpose-built for e-commerce needs. Includes a powerful theme event system and multi-level block nesting to make theme customization seamless, all while maintaining zero dependencies for peak performance.
                    </p>
                    <!-- <div class="cta-buttons">
                        <a href="#get-started" class="btn btn-primary" 
                           aria-label="开始使用 Sline 模板引擎"
                           itemprop="url">
                            🚀 开始使用
                        </a>
                        <a href="https://marketplace.visualstudio.com/items?itemName=liquid-to-sline" 
                           class="btn btn-secondary" 
                           aria-label="下载 Sline VS Code 插件"
                           target="_blank" 
                           rel="noopener noreferrer"
                           itemprop="downloadUrl">
                            📦 获取插件
                        </a>
                    </div> -->
                </div>
            </div>
        </section>

        <!-- 主要内容 -->
        <div class="main-content">
        <!-- 特性区域 -->
        <section id="features" class="section">
            <div class="container">
                <h2 class="section-title" data-i18n="features-title">Key Features</h2>
                <p class="section-subtitle" data-i18n="features-subtitle">Discover the powerful capabilities that make Sline the perfect engine for SHOPLINE Online Store 3.0 themes</p>
                
                <div class="features-grid">
                    <div class="feature-card">
                        <span class="feature-icon">⚡</span>
                        <h3 data-i18n="feature-performance-title">Lightning-Fast Performance</h3>
                        <p data-i18n="feature-performance-desc">Built from the ground up for speed. Our lightweight architecture dramatically reduces load times while maintaining silky-smooth page rendering. Engineered specifically for e-commerce, ensuring your store delivers instant responses to every customer interaction.</p>
                    </div>
                    
                    <div class="feature-card">
                        <span class="feature-icon">🎯</span>
                        <h3 data-i18n="feature-ecommerce-title">E-commerce First Design</h3>
                        <p data-i18n="feature-ecommerce-desc">Purpose-built for SHOPLINE Online Store 3.0 themes with deep e-commerce integration baked in. Every feature is optimized for product showcases, seamless checkout flows, and converting browsers into buyers.</p>
                    </div>
                    
                    <div class="feature-card">
                        <span class="feature-icon">🔧</span>
                        <h3 data-i18n="feature-events-title">Smart Event System</h3>
                        <p data-i18n="feature-events-desc">Advanced theme event system that automatically handles product browsing, variant selection, cart interactions, and more. Create rich, interactive shopping experiences that feel natural and responsive.</p>
                    </div>
                    
                    <div class="feature-card">
                        <span class="feature-icon">🏗️</span>
                        <h3 data-i18n="feature-nesting-title">Flexible Block Architecture</h3>
                        <p data-i18n="feature-nesting-desc">Powerful multi-level block nesting gives you complete control over page layouts and functionality. Build complex, beautiful storefronts with ease – no developer expertise required.</p>
                    </div>
                    
                    <div class="feature-card">
                        <span class="feature-icon">🚀</span>
                        <h3 data-i18n="feature-dependencies-title">Zero Dependencies</h3>
                        <p data-i18n="feature-dependencies-desc">Completely self-contained with no external libraries to slow things down. Pure CSS3 animations replace heavy JavaScript, while smart image loading keeps everything running smoothly.</p>
                    </div>
                    
                    <div class="feature-card">
                        <span class="feature-icon">📈</span>
                        <h3 data-i18n="feature-architecture-title">Built to Scale</h3>
                        <p data-i18n="feature-architecture-desc">Extensible architecture that grows with your needs. Provides a rock-solid foundation that powers every SHOPLINE Online Store 3.0 theme with consistency and reliability.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Syntax Comparison Section -->
        <section id="syntax" class="section">
            <div class="container">
                <h2 class="section-title" data-i18n="syntax-title">Syntax Comparison</h2>
                <p class="section-subtitle" data-i18n="syntax-subtitle">See how Sline's intuitive syntax compares to Liquid and discover what makes SHOPLINE's engine special</p>
                
                <div class="syntax-comparison">
                    <div class="comparison-header">
                        <div class="comparison-title liquid" data-i18n="syntax-liquid">Liquid Syntax</div>
                        <div class="comparison-title sline" data-i18n="syntax-sline">Sline Syntax</div>
                    </div>
                    
                    <div class="comparison-content">
                        <div class="code-panel liquid">
                            <pre class="shiki" id="liquid-code"><code>
{% case user.account.type %}
    {% when 'premium' %}
    Premium user
    {% when 'basic' %}
    Basic user
{% endcase %}

{% for product in products %}
    {{ product.title }}
{% endfor %}

{% unless items.size > 0 %}content{% endunless %}
                            </code></pre>
                        </div>
                        
                        <div class="code-panel sline">
                            <pre class="shiki" id="sline-code"><code>
{{#switch user.account.type}}
    {{#case "premium" /}}
    Premium user
    {{#case "basic" /}}
    Basic user
{{/switch}}

{{#for product in products}}
    {{ product.title }}
{{/for}}

{{#if !(items|size() > 0)}}content{{/if}}
                            </code></pre>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Statistics Section -->
        <section id="stats" class="section stats">
            <div class="container">
                <h2 class="section-title" data-i18n="stats-title">Why Choose Sline</h2>
                <p class="section-subtitle" data-i18n="stats-subtitle">The numbers speak for themselves – here's what makes Sline the smart choice for your store</p>
                
                <div class="stats-grid">
                    <div class="stat-item">
                        <h3 data-i18n="stat-lightweight">Ultra-Light</h3>
                        <p data-i18n="stat-lightweight-desc">Blazing fast load times</p>
                    </div>
                    <div class="stat-item">
                        <h3 data-i18n="stat-zerodep">Zero Dependencies</h3>
                        <p data-i18n="stat-zerodep-desc">No bloated third-party code</p>
                    </div>
                    <div class="stat-item">
                        <h3 data-i18n="stat-professional">E-commerce Ready</h3>
                        <p data-i18n="stat-professional-desc">Built for online stores</p>
                    </div>
                    <div class="stat-item">
                        <h3 data-i18n="stat-enterprise">Enterprise Grade</h3>
                        <p data-i18n="stat-enterprise-desc">Backed by SHOPLINE</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- CTA Section -->
        <section id="get-started" class="section cta-section">
            <div class="container">
                <div class="cta-content">
                    <h2 class="section-title" data-i18n="cta-title">Ready to Get Started?</h2>
                    <p class="section-subtitle" data-i18n="cta-subtitle">
                        Dive deeper into how Sline powers SHOPLINE Online Store 3.0 themes. Check out our detailed comparison with other template engines, or learn how to build amazing themes with SHOPLINE CLI.
                    </p>
                    
                    <div class="cta-buttons">
                        <a style="display: none;" href="Shopify-Dawn-vs-Shopline-Bottle.html" class="btn btn-secondary" data-i18n="cta-comparison">
                            Dawn vs Bottle: Full Comparison
                        </a>
                        <a href="sline-vs-handlebars-vs-liquid.html" class="btn btn-secondary">Sline vs Handlebars vs Liquid</a>
                        <a href="sline-theme-cli.html" class="btn btn-secondary" data-i18n="cta-cli">
                            SHOPLINE CLI: Developer Guide
                        </a>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- 页脚 -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4 data-i18n="footer-product">Product</h4>
                    <p><a href="#features" data-i18n="footer-product-features">Features</a></p>
                    <p><a href="#syntax" data-i18n="footer-product-syntax">Syntax Guide</a></p>
                    <p><a href="#get-started" data-i18n="footer-product-start">Quick Start</a></p>
                </div>
                
                <div class="footer-section">
                    <h4 data-i18n="footer-tools">Developer Tools</h4>
                    <p><a href="#" data-i18n="footer-tools-vscode">Sline Languages Syntax Highlighter</a></p>
                    <p><a href="mcp.html" data-i18n="footer-tools-mcp">Sline MCP Server</a></p>
                    <p><a href="https://hbs2sline.sline.dev" data-i18n="footer-tools-converter">Handlebars to Sline Converter</a></p>
                    <p><a href="#" data-i18n="footer-tools-liquid-to-sline-converter">Liquid to Sline Converter</a></p>
                    <p><a href="#" data-i18n="footer-tools-ast">Sline AST Explorer</a></p>
                    <p><a href="#" data-i18n="footer-tools-analyzer">Type Analyzer</a></p>
                </div>
                
                <div class="footer-section">
                    <h4 data-i18n="footer-community">Community</h4>
                    <p><a href="https://github.com/shoplineos/" data-i18n="footer-community-github">GitHub</a></p>
                    <p><a href="https://github.com/shoplineos/Bottle/issues" data-i18n="footer-community-issues">Report Issues</a></p>
                    <p><a href="https://github.com/shoplineos/Bottle/pulls" data-i18n="footer-community-contribute">Contributing</a></p>
                </div>
                
                <div class="footer-section">
                    <h4 data-i18n="footer-support">Support</h4>
                    <p><a href="#" data-i18n="footer-support-docs">Documentation</a></p>
                    <p><a href="https://developer.shopline.com/docs/sline/object/shop/?version=v20251201" data-i18n="footer-support-api">API Reference</a></p>
                    <p><a href="mailto:<EMAIL>" data-i18n="footer-support-contact">Contact Us</a></p>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p data-i18n="[html]footer-copyright">&copy; 2025 Sline Template Engine. <a href="/disclaimer.html" data-i18n="footer-copyright-link" target="_blank">Disclaimer</a></p>
            </div>
        </div>
    </footer>
    <script type="module" src="/src/index.js"></script>
</body>
</html>
