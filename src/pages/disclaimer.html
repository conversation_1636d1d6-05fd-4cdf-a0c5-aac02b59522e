<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="robots" content="noindex, nofollow, noarchive, nosnippet, noimageindex, nocache">
    <title>sline.dev 免责声明</title>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@200..900&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: "Noto Serif SC", serif;
            font-optical-sizing: auto;
            font-weight: 600;
            font-style: normal;
        }
        /* Custom styles for better typography and readability */
        .prose-custom h1, .prose-custom h2 {
            font-weight: 700;
        }
        .prose-custom section p{
            margin-bottom: 1rem;
        }
        .prose-custom p, .prose-custom li {
            line-height: 1.6;
        }
        .prose-custom a {
            color: #2563eb; /* A nice blue for links in light mode */
            text-decoration: none;
            transition: color 0.2s ease-in-out;
        }
        .prose-custom a:hover {
            color: #1d4ed8;
        }
    </style>
<!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-48E7SQSJDK"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-48E7SQSJDK');
</script>
</head>
<body class="bg-gray-50 text-gray-700 antialiased">

    <!-- Main Container -->
    <div class="container mx-auto max-w-4xl px-4 sm:px-6 lg:px-8 py-12 sm:py-16">
        
        <!-- Disclaimer Card -->
        <div class="bg-white/90 backdrop-blur-sm shadow-xl ring-1 ring-gray-200/50 p-8 sm:p-12 prose-custom">

            <!-- Header -->
            <div class="text-center mb-10">
                <h1 class="text-3xl sm:text-4xl font-bold text-gray-900 mb-2">免责声明</h1>
                <p class="text-gray-600"><a href="https://sline.dev">sline.dev</a></p>
            </div>

            <!-- Introduction -->
            <p class="mb-6">
                欢迎访问 `sline.dev`。在您使用本网站（以下简称"本站"）之前，请务必仔细阅读并理解以下所有条款。您对本站的任何访问或使用行为，均将被视为您已无异议地接受并同意遵守本声明的全部内容。如果您不同意本声明中的任何条款，请立即停止使用本站。
            </p>

            <!-- Section 1: Non-Official Nature -->
            <div class="space-y-6">
                <section class="mt-8">
                    <h2 class="text-xl font-semibold text-gray-900 border-l-4 border-blue-500 pl-4 mb-4">1. 非官方性质与关系澄清</h2>
                    <p>本站是一个由个人开发者独立运营和维护的非官方、非营利性技术学习与分享平台，旨在为开发者社群提供关于 SHOPLINE 平台 <code>Sline</code> 模板引擎的学习资源、技术探讨和实践案例。</p>
                    <p class="font-semibold text-red-700 bg-red-50 border border-red-200 p-4 rounded-lg mt-4"><strong>本站与 SHOPLINE Global Inc. 及其任何关联公司（以下统称为"SHOPLINE"）不存在任何形式的隶属、关联、授权、合作、赞助或背书关系。</strong> 本站的所有内容和观点仅代表运营者个人，不代表 SHOPLINE 的官方立场。</p>
                </section>

                <!-- Section 2: Intellectual Property -->
                <section class="mt-8">
                    <h2 class="text-xl font-semibold text-gray-900 border-l-4 border-blue-500 pl-4 mb-4">2. 知识产权归属与指示性合理使用</h2>
                    <p>本站提及的所有 SHOPLINE 相关商标、服务标记、商品名称、品牌名称、Logo 和产品（包括但不限于 "SHOPLINE"、"Sline" 等）的知识产权，均归属于 SHOPLINE 或其合法的权利人所有。本站无意侵犯其任何知识产权。</p>
                    <p>本站对 "Sline" 名称的使用，仅为识别和指代 SHOPLINE 公司的 <code>Sline</code> 模板引擎这一特定技术主题，属于中华人民共和国、美国及其他相关司法管辖区法律所认可的"指示性合理使用"（Nominative Fair Use）。此种使用仅为实现信息参考、学习、研究和评论之目的，绝不旨在暗示本站与 SHOPLINE 之间存在任何商业联系或官方认可。</p>
                    <p>`sline.dev` 域名仅用于标识本网站的技术主题（即 SHOPLINE 的 Sline 模板引擎），非作为商标使用，无意制造与 SHOPLINE 的关联或误导用户。用户应知悉该域名不具任何官方属性。</p>
                    <p>本站不从事任何商业推广或引流行为，未从 SHOPLINE 或其用户处获取直接或间接经济利益。用户通过本站产生的任何开发行为与本站无关。</p>
                </section>

                <!-- Section 3: Content Accuracy -->
                <section class="mt-8">
                    <h2 class="text-xl font-semibold text-gray-900 border-l-4 border-blue-500 pl-4 mb-4">3. 内容来源、准确性与时效性免责</h2>
                    <p>本站发布的内容主要基于公开可获取的 SHOPLINE 官方开发者文档、公开信息以及运营者个人的学习、研究和开发实践。本站尽力确保所提供信息的准确性和实用性，但<strong>不以任何明示或默示的方式保证所有内容的完全准确性、完整性、可靠性、适用性或时效性。</strong></p>
                    <p>本站内容按"现状"（as is）和"现有"（as available）基础提供。技术和信息可能随时发生变化，本站内容可能无法反映最新的官方更新或最佳实践。用户在参考本站内容时，应自行对照 SHOPLINE 官方文档进行验证，并独立承担因使用或依赖本站信息而可能产生的一切风险和后果。</p>
                    <p>用户因依赖本站信息进行操作（尤其涉及商业决策或开发部署）前，必须直接查阅 SHOPLINE 最新官方文档（https://developer.shopline.com/）。本站不承担因未核实官方信息导致的损失。</p>
                </section>

                <!-- Section 4: No Professional Advice -->
                <section class="mt-8">
                    <h2 class="text-xl font-semibold text-gray-900 border-l-4 border-blue-500 pl-4 mb-4">4. 不构成专业建议声明</h2>
                    <p>本站提供的所有信息，包括但不限于代码示例、教程、分析和观点，<strong>仅供一般性学习和参考之用，不构成任何形式的商业、技术、投资或法律建议。</strong> 用户不应将本站内容作为做出任何商业决策或法律决策的唯一依据。在进行任何可能产生法律或财务影响的行动前，您应咨询具备相应资质的专业人士。</p>
                </section>

                <!-- Section 5: Limitation of Liability -->
                <section class="mt-8">
                    <h2 class="text-xl font-semibold text-gray-900 border-l-4 border-blue-500 pl-4 mb-4">5. 责任限制</h2>
                    <p>在适用法律允许的最大范围内，本站运营者不对任何因使用或无法使用本站所导致的直接、间接、偶然、特殊、惩罚性或后果性损害承担任何责任。这包括但不限于：</p>
                    <ul class="list-disc list-inside mt-4 space-y-2 pl-4">
                        <li>因依赖本站内容而造成的任何损失或损害；</li>
                        <li>因本站的技术错误、中断、延迟或安全漏洞而造成的损失；</li>
                        <li>通过本站链接访问第三方网站而造成的任何损失。</li>
                    </ul>
                    <p class="mt-4">用户明确同意，使用本站的全部风险由用户自行承担。</p>
                </section>

                <!-- Section 6: External Links -->
                <section class="mt-8">
                    <h2 class="text-xl font-semibold text-gray-900 border-l-4 border-blue-500 pl-4 mb-4">6. 外部链接免责</h2>
                    <p>本站可能包含指向第三方网站或资源的链接。提供这些链接仅为方便用户获取更多信息。本站无法控制这些第三方网站的内容、隐私政策或运营实践，因此不对其内容的准确性、合法性或安全性承担任何责任。用户访问和使用任何第三方网站，均需自行承担风险并遵守该网站的使用条款。本站提供链接的行为，不构成对该第三方网站或其内容的任何推荐或认可。</p>
                </section>

                <!-- Section 7: Severability -->
                <section class="mt-8">
                    <h2 class="text-xl font-semibold text-gray-900 border-l-4 border-blue-500 pl-4 mb-4">7. 可分割性条款</h2>
                    <p>如果本免责声明中的任何条款被有管辖权的法院认定为无效、非法或不可执行，该条款应被视为已从本声明中删除，但其余条款的有效性、合法性和可执行性将不受任何影响，并继续保持完全效力。</p>
                </section>
                
                <!-- Section 8: Governing Law -->
                <section class="mt-8">
                    <h2 class="text-xl font-semibold text-gray-900 border-l-4 border-blue-500 pl-4 mb-4">8. 管辖法律与争议解决</h2>
                    <p>本免责声明的解释、有效性及争议解决，均受 <strong>中华人民共和国</strong> 法律的管辖。因使用本站而产生的或与本声明相关的任何争议，各方应首先尝试通过友好协商解决。协商不成的，任何一方均有权将争议提交至 <strong>运营者所在地有管辖权的人民法院</strong> 通过诉讼解决。</p>
                </section>
                <!-- Section 9: Governing Law -->
                <section class="mt-8">
                    <h2 class="text-xl font-semibold text-gray-900 border-l-4 border-blue-500 pl-4 mb-4">9. 知识产权保护与侵权处理</h2>
                    <p>若权利人认为本站内容侵犯其合法权益，请提供以下书面材料至联系邮箱：</p>
                    <ul class="list-disc list-inside mt-4 space-y-2 pl-4">
                        <li>权利证明及身份信息；</li>
                        <li>侵权内容的具体定位信息；</li>
                        <li>侵权声明及法律承诺。</li>
                    </ul>
                    <p class="mt-4">本站承诺在收到完整侵权材料后 48 小时内启动核查并反馈处理结果。若确认侵权，将立即下架相关内容并保留处置记录。</p>
                </section>
                <!-- Section 10: Contact Information -->
                <section class="mt-8">
                    <h2 class="text-xl font-semibold text-gray-900 border-l-4 border-blue-500 pl-4 mb-4">10. 联系方式</h2>
                    <p>如果您对本免责声明有任何疑问，或认为本站内容可能侵犯了您的合法权益，欢迎通过以下方式与我们联系。我们承诺在收到通知后会及时进行核实和处理。</p>
                    <p class="mt-4">
                        <strong>联系邮箱：</strong> <a href="mailto:<EMAIL>" class="font-semibold"><EMAIL></a>
                    </p>
                </section>
            </div>

            <!-- Last Updated -->
            <div class="text-sm text-gray-600 mb-8 border-t border-gray-300 pt-4">
                <strong>最后更新日期：</strong> 2025年07月12日
                <p class="mt-2">本声明可能不定期更新，用户继续使用本站视为接受修订条款。建议定期查阅最新版本。</p>
            </div>

        </div>
        
        <!-- Footer -->
        <footer class="text-center mt-8 text-sm text-gray-600">
            <p>&copy; 2025 <a href="https://sline.dev">sline.dev</a>. All rights reserved.</p>
        </footer>

    </div>
</body>
</html>
