<!DOCTYPE html>
<html lang="zh-CN" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shopline 战略转型：从模仿到创新的深度解析</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Noto Sans SC', sans-serif;
            background-color: #FDFBF8;
            color: #38332E;
        }
        .chart-container {
            position: relative;
            width: 100%;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
            height: 300px;
            max-height: 400px;
        }
        @media (min-width: 768px) {
            .chart-container {
                height: 400px;
            }
        }
        .nav-link {
            position: relative;
            transition: color 0.3s;
        }
        .nav-link::after {
            content: '';
            position: absolute;
            width: 100%;
            transform: scaleX(0);
            height: 2px;
            bottom: -2px;
            left: 0;
            background-color: #004D40;
            transform-origin: bottom right;
            transition: transform 0.3s ease-out;
        }
        .nav-link:hover, .nav-link.active {
            color: #004D40;
        }
        .nav-link:hover::after, .nav-link.active::after {
            transform: scaleX(1);
            transform-origin: bottom left;
        }
        .strategy-card {
            background-color: white;
            border: 1px solid #EAE5E0;
            cursor: pointer;
            transition: transform 0.3s, box-shadow 0.3s;
            min-height: 220px;
        }
        .strategy-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.05), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        .fade-in {
            animation: fadeIn 0.5s ease-in-out;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        #pillar-details ul {
            list-style-position: inside;
            padding-left: 0.5rem;
        }
        #pillar-details ul li {
            margin-bottom: 0.5rem;
        }
        #pillar-details h6 {
            font-weight: bold;
            color: #38332E;
            margin-top: 1rem;
            margin-bottom: 0.5rem;
        }
    </style>
</head>
<body class="antialiased">

    <header id="header" class="bg-[#FDFBF8]/80 backdrop-blur-sm sticky top-0 z-50 transition-all duration-300 shadow-sm">
        <nav class="container mx-auto px-6 py-3 flex justify-between items-center">
            <h1 class="text-xl md:text-2xl font-bold text-[#004D40]">Shopline 战略转型</h1>
            <div class="hidden md:flex space-x-8">
                <a href="#analysis-pillars" class="nav-link font-medium">战略支柱</a>
                <a href="#engine-comparison" class="nav-link font-medium">引擎对决</a>
                <a href="#strategy-matrix" class="nav-link font-medium">转型矩阵</a>
                <a href="#conclusion" class="nav-link font-medium">结论展望</a>
            </div>
            <div class="md:hidden">
                 <button id="menu-btn" class="text-[#004D40] focus:outline-none">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16m-7 6h7"></path></svg>
                </button>
            </div>
        </nav>
        <div id="mobile-menu" class="hidden md:hidden">
            <a href="#analysis-pillars" class="block py-2 px-6 text-sm hover:bg-gray-200/50">战略支柱</a>
            <a href="#engine-comparison" class="block py-2 px-6 text-sm hover:bg-gray-200/50">引擎对决</a>
            <a href="#strategy-matrix" class="block py-2 px-6 text-sm hover:bg-gray-200/50">转型矩阵</a>
            <a href="#conclusion" class="block py-2 px-6 text-sm hover:bg-gray-200/50">结论展望</a>
        </div>
    </header>

    <main class="container mx-auto px-6 py-8 md:py-16">
        <section id="hero" class="text-center mb-16 md:mb-24">
            <h2 class="text-3xl md:text-5xl font-bold mb-4 leading-tight">从被迫应诉到主动破局</h2>
            <p class="text-lg md:text-xl max-w-3xl mx-auto text-gray-600">
                一场来自 Shopify 的“全盘抄袭”诉讼，成为 Shopline 的催化剂。这不仅是一次技术更迭，更是一场将法律危机转化为核心竞争力的战略杰作，彻底重塑了其技术、法律、经济与竞争格局。
            </p>
        </section>

        <section id="analysis-pillars" class="mb-16 md:mb-24">
             <div class="text-center mb-12">
                <h3 class="text-2xl md:text-3xl font-bold">转型的四大战略支柱</h3>
                <p class="mt-2 text-gray-600 max-w-2xl mx-auto">Shopline 放弃 Liquid/Handlebars 并自研 Sline 引擎的决策，是基于四个维度的深思熟虑。点击下方卡片，探索每一个驱动因素背后的详细分析。</p>
            </div>
            <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
                <div id="pillar-legal" class="p-6 rounded-lg shadow-md bg-white border border-gray-200/80 cursor-pointer hover:shadow-xl transition-shadow duration-300">
                    <div class="flex items-center text-[#A58E74] mb-3">
                        <span class="text-3xl">⚖️</span>
                        <h4 class="ml-3 text-xl font-bold">法律与合规</h4>
                    </div>
                    <p class="text-gray-600 text-sm">面对 Shopify 的侵权诉讼，自研引擎是规避法律风险、宣告技术主权的唯一出路，将公司从生存危机中解脱出来。</p>
                </div>
                <div id="pillar-tech" class="p-6 rounded-lg shadow-md bg-white border border-gray-200/80 cursor-pointer hover:shadow-xl transition-shadow duration-300">
                    <div class="flex items-center text-[#A58E74] mb-3">
                        <span class="text-3xl">⚙️</span>
                        <h4 class="ml-3 text-xl font-bold">技术与工程</h4>
                    </div>
                    <p class="text-gray-600 text-sm">克服 Liquid 的性能瓶颈和 Handlebars 的安全漏洞。基于Go语言的 Sline 带来了编译级性能、原生并发和架构级安全。</p>
                </div>
                <div id="pillar-econ" class="p-6 rounded-lg shadow-md bg-white border border-gray-200/80 cursor-pointer hover:shadow-xl transition-shadow duration-300">
                    <div class="flex items-center text-[#A58E74] mb-3">
                        <span class="text-3xl">💰</span>
                        <h4 class="ml-3 text-xl font-bold">经济与成本</h4>
                    </div>
                    <p class="text-gray-600 text-sm">将主题引擎从一项高昂的“隐性成本中心”（维护、风险）转变为可增值的核心知识产权资产，并构建起人才护城河。</p>
                </div>
                <div id="pillar-strategy" class="p-6 rounded-lg shadow-md bg-white border border-gray-200/80 cursor-pointer hover:shadow-xl transition-shadow duration-300">
                    <div class="flex items-center text-[#A58E74] mb-3">
                        <span class="text-3xl">♟️</span>
                        <h4 class="ml-3 text-xl font-bold">战略与竞争</h4>
                    </div>
                    <p class="text-gray-600 text-sm">摆脱“模仿者”标签，实现产品差异化。用“集成高性能”方案精准打击 Headless 痛点，并锁定客户与开发者生态。</p>
                </div>
            </div>
            <div id="pillar-details" class="mt-8 p-8 bg-white rounded-lg shadow-inner border border-gray-200/80 hidden">
                <h5 id="details-title" class="text-2xl font-bold mb-4 text-[#004D40]"></h5>
                <div id="details-content" class="text-gray-700 leading-relaxed"></div>
            </div>
        </section>

        <section id="engine-comparison" class="mb-16 md:mb-24">
            <div class="text-center mb-12">
                <h3 class="text-2xl md:text-3xl font-bold">新旧引擎的技术对决</h3>
                <p class="mt-2 text-gray-600 max-w-2xl mx-auto">Sline 引擎相较于 Liquid 和 Handlebars 实现了代际飞跃。点击下方按钮，直观对比它们在关键技术维度的表现。</p>
            </div>
            <div class="bg-white p-6 rounded-lg shadow-lg border border-gray-200/80">
                <div class="flex justify-center space-x-2 md:space-x-4 mb-6">
                    <button data-engine="liquid" class="engine-btn px-4 py-2 text-sm md:text-base font-semibold rounded-md transition-all duration-300 bg-[#A58E74] text-white">Liquid</button>
                    <button data-engine="handlebars" class="engine-btn px-4 py-2 text-sm md:text-base font-semibold rounded-md transition-all duration-300 bg-gray-200 text-gray-700">Handlebars</button>
                    <button data-engine="sline" class="engine-btn px-4 py-2 text-sm md:text-base font-semibold rounded-md transition-all duration-300 bg-gray-200 text-gray-700">Sline (Go)</button>
                </div>
                <div class="chart-container">
                    <canvas id="engine-chart"></canvas>
                </div>
            </div>
        </section>

        <section id="strategy-matrix" class="mb-16 md:mb-24">
            <div class="text-center mb-12">
                <h3 class="text-2xl md:text-3xl font-bold">一石四鸟：Sline 转型战略矩阵</h3>
                <p class="mt-2 text-gray-600 max-w-2xl mx-auto">每一次危机都蕴含着机遇。点击卡片，查看 Shopline 如何在四大维度上，将“问题”巧妙地转化为“解决方案”。</p>
            </div>
            <div class="grid md:grid-cols-2 gap-8">
                <div class="strategy-card rounded-lg p-6 flex flex-col justify-center items-center text-center" data-matrix="legal">
                    <div class="content-front">
                        <span class="text-5xl mb-4">⚖️</span>
                        <h4 class="text-xl font-bold">法律与合规</h4>
                        <p class="text-sm text-gray-500 mt-2">点击揭示问题与对策</p>
                    </div>
                    <div class="content-back hidden">
                        <h5 class="font-bold text-red-600 mb-2">问题</h5>
                        <p class="text-sm mb-4">来自 Shopify 的侵权诉讼构成生存威胁，业务连续性面临巨大风险。</p>
                        <h5 class="font-bold text-green-700 mb-2">Sline 解决方案</h5>
                        <p class="text-sm">创建法律上可辩护的专有技术，宣告技术独立，化解危机。</p>
                    </div>
                </div>
                <div class="strategy-card rounded-lg p-6 flex flex-col justify-center items-center text-center" data-matrix="tech">
                    <div class="content-front">
                        <span class="text-5xl mb-4">⚙️</span>
                        <h4 class="text-xl font-bold">技术与工程</h4>
                        <p class="text-sm text-gray-500 mt-2">点击揭示问题与对策</p>
                    </div>
                    <div class="content-back hidden">
                        <h5 class="font-bold text-red-600 mb-2">问题</h5>
                        <p class="text-sm mb-4">依赖旧引擎，存在性能瓶颈和严重安全漏洞，受制于人。</p>
                        <h5 class="font-bold text-green-700 mb-2">Sline 解决方案</h5>
                        <p class="text-sm">采用编译型 Go 语言，实现性能飞跃，根除特定安全风险。</p>
                    </div>
                </div>
                <div class="strategy-card rounded-lg p-6 flex flex-col justify-center items-center text-center" data-matrix="econ">
                    <div class="content-front">
                        <span class="text-5xl mb-4">💰</span>
                        <h4 class="text-xl font-bold">经济与成本</h4>
                        <p class="text-sm text-gray-500 mt-2">点击揭示问题与对策</p>
                    </div>
                    <div class="content-back hidden">
                         <h5 class="font-bold text-red-600 mb-2">问题</h5>
                        <p class="text-sm mb-4">主题技术是高昂的成本中心（维护、法律风险），甚至是负债。</p>
                        <h5 class="font-bold text-green-700 mb-2">Sline 解决方案</h5>
                        <p class="text-sm">将技术从负债转化为核心IP资产，构建人力资本护城河。</p>
                    </div>
                </div>
                <div class="strategy-card rounded-lg p-6 flex flex-col justify-center items-center text-center" data-matrix="strategy">
                    <div class="content-front">
                        <span class="text-5xl mb-4">♟️</span>
                        <h4 class="text-xl font-bold">战略与竞争</h4>
                        <p class="text-sm text-gray-500 mt-2">点击揭示问题与对策</p>
                    </div>
                    <div class="content-back hidden">
                        <h5 class="font-bold text-red-600 mb-2">问题</h5>
                        <p class="text-sm mb-4">陷入“模仿者”困境，缺乏产品差异化，生态粘性不足。</p>
                        <h5 class="font-bold text-green-700 mb-2">Sline 解决方案</h5>
                        <p class="text-sm">实现根本性创新，提高转换成本，锁定客户，精准打击竞品痛点。</p>
                    </div>
                </div>
            </div>
        </section>

        <section id="conclusion" class="text-center pt-8 border-t border-gray-200">
            <h3 class="text-2xl md:text-3xl font-bold">结论：一场定义未来的战役</h3>
            <div class="max-w-3xl mx-auto mt-4 space-y-4 text-gray-600">
                <p>Shopline 从放弃 Liquid/Handlebars 到自研 Sline 的转变，是一次由法律危机触发，但由深思熟虑的技术、经济和战略考量驱动的全面转型。这不仅是一次技术更新，更是 Shopline 试图摆脱竞争对手阴影、定义自身在下一代电商平台战争中角色的决定性战役。</p>
                <p class="font-semibold text-[#004D40]">Sline 的成功将验证一种新的平台模式——在保持传统主题系统易用性的同时，提供接近 Headless 的高性能，这可能对未来电商平台的架构选择产生深远影响。</p>
            </div>
        </section>

    </main>

    <footer class="bg-gray-100 mt-16">
        <div class="container mx-auto px-6 py-4 text-center text-gray-500 text-sm">
            <p>&copy; 2024 交互式分析报告。内容基于公开信息分析，仅供参考。</p>
        </div>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Data store
            const pillarDetailsData = {
                legal: {
                    title: '⚖️ 法律与合规：生存之战',
                    content: `
                        <p class="mb-4">Shopify 提起的“全盘抄袭”诉讼，将 Shopline 推到生死存亡的边缘。其旗舰主题“Seed”被指控为 Shopify“Dawn”主题的“山寨版”，直接威胁到 Shopline 的核心产品和业务连续性。</p>
                        <h6>核心指控：</h6>
                        <ul class="list-disc">
                            <li><strong>架构级抄袭：</strong> 不仅是视觉相似，诉讼直指 Shopline 系统性地将 Dawn 主题代码“翻译”成另一种语言，这是深度的版权侵犯。</li>
                            <li><strong>路径依赖风险：</strong> “快速跟随者”战略的风险彻底暴露。继续使用相似的技术栈（Liquid/Handlebars）意味着将永久暴露在未来的侵权指控之下。</li>
                        </ul>
                        <h6>Sline 的角色：技术主权宣言</h6>
                        <p>开发一套全新的、基于 Go 语言的专有引擎 Sline，是一项无可辩驳的原创性工作。此举将叙事从“抄袭者”转变为“被迫创新者”，为法律团队提供了强有力的论据，是面向法院、投资者和市场的、公开且不可逆转的技术主权宣言。</p>
                    `
                },
                tech: {
                    title: '⚙️ 技术与工程：追求卓越',
                    content: `
                        <p class="mb-4">法律是催化剂，但技术决策本身是深思熟虑的。Sline 旨在解决旧引擎在性能和安全上的根本性缺陷。</p>
                        <h6>旧引擎的技术负债：</h6>
                        <ul class="list-disc">
                            <li><strong>Liquid 性能瓶颈：</strong> 作为解释型语言，性能受限。Shopify 自身都需开发 C 语言扩展（Liquid-C）来加速，并强制 256KB 文件大小限制，证明其存在性能天花板。</li>
                            <li><strong>Handlebars 安全漏洞：</strong> 作为一个广泛使用的 JS 库，其安全漏洞历史丰富且严重，包括远程代码执行（RCE）、原型链污染等，对电商平台构成重大威胁。</li>
                        </ul>
                        <h6>Sline 的架构优势：</h6>
                        <ul class="list-disc">
                            <li><strong>编译型语言的力量：</strong> Go 语言作为编译型语言，能生成高度优化的机器码，在性能和原生并发处理能力上远超解释型或 JIT 语言。</li>
                            <li><strong>预编译架构（推测）：</strong> Sline 极可能是预编译引擎，将主题直接编译为原生 Go 代码执行，完全绕过解释器开销，实现对 Liquid 的根本性性能超越。</li>
                            <li><strong>技术栈统一：</strong> 后端和主题引擎均采用 Go，可降低开发复杂性、减少技术债务，提升长期工程效率。</li>
                        </ul>
                    `
                },
                econ: {
                    title: '💰 经济与成本：资产重塑',
                    content: `
                        <p class="mb-4">自研 Sline 表面上投入巨大，但从总拥有成本（TCO）来看，是一项财务上极其审慎的决策。</p>
                        <h6>隐性成本的显性化：</h6>
                        <ul class="list-disc">
                            <li><strong>高昂的 TCO：</strong> 使用开源技术的成本远不止授权费，还包括持续的性能优化、安全维护，以及最终爆发的、数百万美元的法律诉讼风险。</li>
                            <li><strong>从成本中心到核心资产：</strong> Sline 将主题技术从一个潜在的“法律负债”和“成本中心”，转变为一项可被估值的、能提升公司整体估值的核心知识产权（IP）资产，符合其母公司欢聚集团的战略。</li>
                        </ul>
                        <h6>人力资本护城河：</h6>
                        <p>通过培育专业的 Sline/Go 开发者生态，Shopline 正在构建由人力资本组成的、具有高转换成本的护城河。Sline 专家的技能无法直接迁移，这增加了开发者和服务商对平台的忠诚度，形成“金手铐”效应。</p>
                    `
                },
                strategy: {
                    title: '♟️ 战略与竞争：身份定义',
                    content: `
                        <p class="mb-4">Sline 是 Shopline 摆脱“模仿者”标签，在竞争中定义自己身份的关键武器。</p>
                        <h6>核心竞争策略：</h6>
                        <ul class="list-disc">
                            <li><strong>摆脱功能对标陷阱：</strong> 专有高性能引擎 Sline 为开发 Shopify 难以复制的、计算密集型（如 AI 驱动的实时个性化）功能奠定了基础，实现真正的产品差异化。</li>
                            <li><strong>构建“粘性”生态：</strong> Sline 提高了商户和开发者的转换成本。投资于 Sline 的资产和技能无法轻易迁移到 Shopify，从而锁定了生态系统内的参与者。</li>
                            <li><strong>反击 Headless 趋势：</strong> Sline 精准打击了市场痛点，提供了一个极具吸引力的价值主张：“无需头疼，尽享无头之速”——即在传统主题的易用性框架内，提供接近 Headless 的高性能。</li>
                        </ul>
                    `
                }
            };
            
            const engineData = {
                labels: ['性能', '安全性', '并发能力', '开发效率', '可扩展性'],
                liquid: {
                    label: 'Liquid',
                    data: [4, 7, 3, 8, 6],
                    borderColor: 'rgba(165, 142, 116, 1)',
                    backgroundColor: 'rgba(165, 142, 116, 0.2)',
                },
                handlebars: {
                    label: 'Handlebars',
                    data: [6, 3, 5, 7, 7],
                    borderColor: 'rgba(107, 114, 128, 1)',
                    backgroundColor: 'rgba(107, 114, 128, 0.2)',
                },
                sline: {
                    label: 'Sline (Go)',
                    data: [10, 9, 10, 8, 9],
                    borderColor: 'rgba(0, 77, 64, 1)',
                    backgroundColor: 'rgba(0, 77, 64, 0.2)',
                }
            };

            // Mobile menu toggle
            const menuBtn = document.getElementById('menu-btn');
            const mobileMenu = document.getElementById('mobile-menu');
            menuBtn.addEventListener('click', () => {
                mobileMenu.classList.toggle('hidden');
            });
            mobileMenu.addEventListener('click', () => {
                mobileMenu.classList.add('hidden');
            });

            // Analysis Pillars Interaction
            const pillars = document.querySelectorAll('[id^="pillar-"]');
            const detailsContainer = document.getElementById('pillar-details');
            const detailsTitle = document.getElementById('details-title');
            const detailsContent = document.getElementById('details-content');

            pillars.forEach(pillar => {
                pillar.addEventListener('click', () => {
                    const pillarKey = pillar.id.split('-')[1];
                    const data = pillarDetailsData[pillarKey];
                    
                    detailsTitle.textContent = data.title;
                    detailsContent.innerHTML = data.content;
                    
                    if (detailsContainer.classList.contains('hidden')) {
                        detailsContainer.classList.remove('hidden');
                    }
                    detailsContainer.classList.add('fade-in');
                    detailsContainer.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
                    
                    setTimeout(() => detailsContainer.classList.remove('fade-in'), 500);
                });
            });


            // Engine Comparison Chart
            const ctx = document.getElementById('engine-chart').getContext('2d');
            const engineChart = new Chart(ctx, {
                type: 'radar',
                data: {
                    labels: engineData.labels,
                    datasets: [{
                        label: engineData.liquid.label,
                        data: engineData.liquid.data,
                        borderColor: engineData.liquid.borderColor,
                        backgroundColor: engineData.liquid.backgroundColor,
                        borderWidth: 2,
                        pointBackgroundColor: engineData.liquid.borderColor,
                        pointRadius: 4,
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        r: {
                            angleLines: {
                                color: 'rgba(56, 51, 46, 0.1)'
                            },
                            grid: {
                                color: 'rgba(56, 51, 46, 0.1)'
                            },
                            pointLabels: {
                                font: {
                                    size: 12,
                                    family: "'Noto Sans SC', sans-serif"
                                },
                                color: '#38332E'
                            },
                            ticks: {
                                backdropColor: 'rgba(253, 251, 248, 0.75)',
                                color: '#575049',
                                stepSize: 2,
                                font: {
                                   size: 10
                                }
                            },
                            min: 0,
                            max: 10
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top',
                            labels: {
                                font: {
                                    size: 14,
                                    family: "'Noto Sans SC', sans-serif",
                                    weight: 'bold'
                                },
                                color: '#38332E'
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    let label = context.dataset.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    if (context.parsed.r !== null) {
                                        label += context.parsed.r;
                                    }
                                    return label;
                                }
                            }
                        }
                    }
                }
            });

            const engineButtons = document.querySelectorAll('.engine-btn');
            engineButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const engine = button.dataset.engine;
                    const selectedData = engineData[engine];

                    engineChart.data.datasets[0] = {
                        label: selectedData.label,
                        data: selectedData.data,
                        borderColor: selectedData.borderColor,
                        backgroundColor: selectedData.backgroundColor,
                        borderWidth: 2,
                        pointBackgroundColor: selectedData.borderColor,
                        pointRadius: 4,
                    };
                    engineChart.update();

                    engineButtons.forEach(btn => {
                        btn.classList.remove('bg-[#A58E74]', 'text-white');
                        btn.classList.add('bg-gray-200', 'text-gray-700');
                    });
                    button.classList.add('bg-[#A58E74]', 'text-white');
                    button.classList.remove('bg-gray-200', 'text-gray-700');
                });
            });

            // Strategy Matrix Interaction
            const strategyCards = document.querySelectorAll('.strategy-card');
            strategyCards.forEach(card => {
                card.addEventListener('click', () => {
                    const front = card.querySelector('.content-front');
                    const back = card.querySelector('.content-back');
                    
                    front.classList.toggle('hidden');
                    back.classList.toggle('hidden');
                    back.classList.add('fade-in');
                });
            });
            
            // Active Nav Link on Scroll
            const sections = document.querySelectorAll('section');
            const navLinks = document.querySelectorAll('.nav-link');

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        navLinks.forEach(link => {
                            link.classList.remove('active');
                            if (link.getAttribute('href').substring(1) === entry.target.id) {
                                link.classList.add('active');
                            }
                        });
                    }
                });
            }, { rootMargin: "-50% 0px -50% 0px" });

            sections.forEach(section => {
                observer.observe(section);
            });

        });
    </script>
</body>
</html>