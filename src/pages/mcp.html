<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Sline MCP Server</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@200..900&family=Spectral:ital,wght@0,200;0,300;0,400;0,500;0,600;0,700;0,800;1,200;1,300;1,400;1,500;1,600;1,700;1,800&display=swap" rel="stylesheet">
    <style>
      body {
        font-family: 'Spectral', serif;
        font-size: 14px;
        line-height: 1.6;
        margin: 0;
        padding: 20px;
      }
    </style>
</head>

<body>
  <h1>Sline MCP Server</h1>
  <p>Connect to: `https://mcp.sline.dev/mcp` (or /sse)</p>
  <p>Powered by <a href="https://sline.dev">https://sline.dev</a></p>
  <div style="margin-top: 50px;">
    <pre><strong>Setup Instructions:</strong>

<strong>For most clients:</strong>
{
  "mcpServers": {
    "sline": {
      "serverUrl": "https://mcp.sline.dev/sse"
    }
  }
}

<strong>For private repos:</strong>
{
  "mcpServers": {
    "sline": {
      "serverUrl": "https://mcp.sline.dev/sse",
      "headers": {
        "Authorization": "Bearer &lt;API_KEY&gt;"
      }
    }
  }
}


<strong>For Claude Code:</strong>
claude mcp add -s user -t http sline https://mcp.sline.dev/mcp</pre>
  </div>
</body>

</html>