<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <!-- 基础SEO标签 -->
    <title>SHOPLINE CLI开发指南 | Bottle主题开发教程 | 现代化电商主题开发工具</title>
    <meta name="description" content="完整的SHOPLINE CLI开发指南，包含Bottle主题开发教程、命令详解、最佳实践。学习如何使用CLI工具高效开发SHOPLINE Online Store 3.0主题，实现本地实时预览和团队协作。">
    <meta name="keywords" content="SHOPLINE CLI, Bottle主题, 电商主题开发, 主题开发工具, SHOPLINE开发指南, 在线商店主题, Sline模板引擎, 主题开发教程">
    <meta name="author" content="SHOPLINE">
    <meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1">
    <link rel="canonical" href="https://sline.dev/sline-theme-cli.html">

    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">

    <!-- Open Graph 标签 -->
    <meta property="og:type" content="article">
    <meta property="og:title" content="SHOPLINE CLI开发指南 | Bottle主题开发教程">
    <meta property="og:description" content="完整的SHOPLINE CLI开发指南，包含Bottle主题开发教程、命令详解、最佳实践。学习如何使用CLI工具高效开发SHOPLINE主题。">
    <meta property="og:url" content="https://sline.dev/sline-theme-cli.html">
    <meta property="og:site_name" content="Sline.dev - SHOPLINE开发者资源">
    <meta property="og:image" content="https://sline.dev/sline-logo.png">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <meta property="og:image:alt" content="SHOPLINE CLI开发指南封面图">
    <meta property="og:locale" content="zh_CN">
    <meta property="article:author" content="SHOPLINE开发团队">
    <meta property="article:published_time" content="2024-01-01T00:00:00+08:00">
    <meta property="article:modified_time" content="2024-01-01T00:00:00+08:00">
    <meta property="article:section" content="开发教程">
    <meta property="article:tag" content="SHOPLINE CLI">
    <meta property="article:tag" content="主题开发">
    <meta property="article:tag" content="Bottle主题">
    
    <!-- Twitter Cards -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="SHOPLINE CLI开发指南 | Bottle主题开发教程">
    <meta name="twitter:description" content="完整的SHOPLINE CLI开发指南，包含Bottle主题开发教程、命令详解、最佳实践。">
    <meta name="twitter:image" content="https://sline.dev/sline-logo.png">
    <meta name="twitter:image:alt" content="SHOPLINE CLI开发指南">
    
    <!-- 技术SEO标签 -->
    <meta name="theme-color" content="#3B82F6">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="SHOPLINE CLI指南">
    <meta name="format-detection" content="telephone=no">
    
    <!-- 预连接优化 -->
    <link rel="preconnect" href="https://fonts.googleapis.com" crossorigin>
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- DNS预解析 -->
    <link rel="dns-prefetch" href="//developer.shopline.com">
    <link rel="dns-prefetch" href="//github.com">
    <link rel="dns-prefetch" href="//nodejs.org">
    
    <!-- 结构化数据 -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "TechArticle",
      "headline": "SHOPLINE CLI开发指南 | Bottle主题开发教程",
      "description": "完整的SHOPLINE CLI开发指南，包含Bottle主题开发教程、命令详解、最佳实践。学习如何使用CLI工具高效开发SHOPLINE Online Store 3.0主题。",
      "image": "https://sline.dev/sline-logo.png",
      "author": {
        "@type": "Organization",
        "name": "SHOPLINE",
        "url": "https://shopline.com"
      },
      "publisher": {
        "@type": "Organization",
        "name": "Sline.dev",
        "logo": {
          "@type": "ImageObject",
          "url": "https://sline.dev/sline-logo.png",
          "width": 300,
          "height": 60
        }
      },
      "datePublished": "2024-01-01T00:00:00+08:00",
      "dateModified": "2024-01-01T00:00:00+08:00",
      "mainEntityOfPage": {
        "@type": "WebPage",
        "@id": "https://sline.dev/sline-theme-cli.html"
      },
      "proficiencyLevel": "Beginner",
      "dependencies": ["Node.js", "Git", "SHOPLINE CLI"],
      "operatingSystem": "Cross-platform",
      "programmingLanguage": "JavaScript",
      "about": [
        {
          "@type": "Thing",
          "name": "SHOPLINE CLI",
          "description": "SHOPLINE命令行开发工具"
        },
        {
          "@type": "Thing", 
          "name": "Bottle主题",
          "description": "SHOPLINE官方示例主题"
        },
        {
          "@type": "Thing",
          "name": "主题开发",
          "description": "电商网站主题开发流程"
        }
      ],
      "mentions": [
        {
          "@type": "SoftwareApplication",
          "name": "SHOPLINE CLI",
          "applicationCategory": "DeveloperApplication",
          "operatingSystem": "Cross-platform"
        }
      ]
    }
    </script>
    
    <!-- 面包屑导航结构化数据 -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "BreadcrumbList",
      "itemListElement": [
        {
          "@type": "ListItem",
          "position": 1,
          "name": "首页",
          "item": "https://sline.dev/"
        },
        {
          "@type": "ListItem",
          "position": 2,
          "name": "开发指南",
          "item": "https://sline.dev/docs/"
        },
        {
          "@type": "ListItem",
          "position": 3,
          "name": "SHOPLINE CLI指南",
          "item": "https://sline.dev/sline-theme-cli.html"
        }
      ]
    }
    </script>
    
    <!-- FAQ结构化数据 -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "什么是SHOPLINE CLI？",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "SHOPLINE CLI是一个命令行开发工具，用于本地开发和管理SHOPLINE Online Store 3.0主题。它提供实时预览、代码同步、团队协作等功能。"
          }
        },
        {
          "@type": "Question",
          "name": "如何安装SHOPLINE CLI？",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "使用npm或yarn全局安装：npm install --global @shoplineos/cli 或 yarn global add @shoplineos/cli"
          }
        },
        {
          "@type": "Question",
          "name": "SHOPLINE CLI支持哪些核心命令？",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "主要命令包括：init（初始化主题）、serve（启动开发服务器）、push（上传主题）、pull（下载主题）、check（代码检查）、package（打包主题）。"
          }
        }
      ]
    }
    </script>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;900&family=Noto+Serif+SC:wght@400;500;700;900&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Noto Serif SC', 'Inter', serif;
            background-color: #F8F5F2;
            font-weight: 500;
            color: #374151;
        }
        .nav-link {
            transition: color 0.3s, border-bottom-color 0.3s;
            border-bottom: 2px solid transparent;
        }
        .nav-link:hover, .nav-link.active {
            color: #3B82F6;
            border-bottom-color: #3B82F6;
        }
        .command-tab.active {
            background-color: #3B82F6;
            color: #FFFFFF;
        }
        .command-tab {
            transition: background-color 0.3s, color 0.3s;
        }
        .workflow-step.active {
            transform: scale(1.05);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5);
            border-color: #3B82F6;
        }
        .workflow-step {
            transition: transform 0.3s, box-shadow 0.3s, border-color 0.3s;
            cursor: pointer;
        }
        .section-fade-in {
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.6s ease-out, transform 0.6s ease-out;
        }
        .section-fade-in.visible {
            opacity: 1;
            transform: translateY(0);
        }
        .workflow-toggle-btn.active {
            background-color: #3B82F6;
            color: #FFFFFF;
        }
        .workflow-toggle-btn {
            transition: background-color 0.3s, color 0.3s;
        }
    </style>
<!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-48E7SQSJDK"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-48E7SQSJDK');
</script>
</head>
<body class="antialiased leading-relaxed">

    <!-- Header & Navigation -->
    <header id="header" class="bg-white/80 backdrop-blur-md sticky top-0 z-50 shadow-sm">
        <div class="container max-w-7xl mx-auto px-4">
            <nav class="flex justify-between items-center py-4">
                <div class="text-xl font-bold text-gray-800">
                    <a href="https://sline.dev/sline-theme-cli.html">SHOPLINE CLI 指南 (Bottle版)</a>
                </div>
                <div class="hidden md:flex space-x-6 lg:space-x-8">
                    <a href="#get-started" class="nav-link py-2">快速上手</a>
                    <a href="#commands" class="nav-link py-2">核心命令</a>
                    <a href="#directory-structure" class="nav-link py-2">目录结构</a>
                    <a href="#workflows" class="nav-link py-2">开发工作流</a>
                    <a href="#best-practices" class="nav-link py-2">最佳实践</a>
                </div>
                <div class="md:hidden">
                    <button id="mobile-menu-button" class="text-gray-600 focus:outline-none">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16m-7 6h7"></path></svg>
                    </button>
                </div>
            </nav>
            <div id="mobile-menu" class="hidden md:hidden py-2">
                <a href="#get-started" class="block nav-link py-2 text-center">快速上手</a>
                <a href="#commands" class="block nav-link py-2 text-center">核心命令</a>
                <a href="#directory-structure" class="block nav-link py-2 text-center">目录结构</a>
                <a href="#workflows" class="block nav-link py-2 text-center">开发工作流</a>
                <a href="#best-practices" class="block nav-link py-2 text-center">最佳实践</a>
            </div>
        </div>
    </header>

    <main class="container mx-auto px-4 py-8 md:py-16" role="main">
        <!-- Hero Section -->
        <section id="hero" class="text-center mb-16 md:mb-24 section-fade-in" aria-labelledby="hero-title">
            <h1 id="hero-title" class="text-4xl md:text-6xl font-bold text-gray-900 mb-4">开启现代化主题开发之旅</h1>
            <p class="text-lg md:text-xl text-gray-600 max-w-5xl mx-auto">
                告别繁琐的手动上传和刷新。借助SHOPLINE CLI，您可以像专业开发者一样，在本地实时预览、高效同步、轻松协作，释放SHOPLINE Online Store 3.0主题的全部潜力。
            </p>
        </section>

        <!-- Get Started Section -->
        <section id="get-started" class="mb-16 md:mb-24 section-fade-in" aria-labelledby="get-started-title">
            <h2 id="get-started-title" class="text-3xl md:text-4xl font-bold text-center mb-8 md:mb-12">三步快速上手</h2>
             <div class="max-w-7xl mx-auto bg-white p-8 rounded-xl shadow-lg">
                <div class="intro-paragraph mb-8">
                    <p class="text-gray-600">在开始您的第一个SHOPLINE主题开发项目之前，只需几个简单的步骤来设置您的开发环境。这个过程将为您安装必要的工具，并将您的本地机器与您的SHOPLINE店铺安全地连接起来。完成这些准备工作后，您就可以使用CLI提供的所有强大功能了。</p>
                    <div class="mt-4 p-4 bg-blue-50 rounded-lg">
                        <h4 class="font-semibold text-blue-800 mb-2">📚 准备工作清单：</h4>
                        <ul class="text-sm text-blue-700 space-y-1">
                            <li>• 安装 Git 和 Node.js</li>
                            <li>• 确保您有一个开发者店铺，进行开发调试</li>
                            <li>• 确保您已连接到互联网。大多数 SHOPLINE CLI 命令需要互联网连接才能运行</li>
                            <li>• 了解 <strong>Sline 模板引擎</strong> - SHOPLINE自研的模板引擎，是主题在商家店面中呈现的基石，用于动态渲染商家店铺页面</li>
                        </ul>
                    </div>
                </div>
                <ol class="space-y-8">
                    <li class="flex items-start space-x-4">
                        <div class="flex-shrink-0 w-12 h-12 bg-blue-500 text-white rounded-full flex items-center justify-center text-xl font-bold">1</div>
                        <div>
                            <h3 class="text-xl font-semibold mb-2">安装环境</h3>
                            <p class="text-gray-600 mb-4">确保您的电脑已安装 <a href="https://git-scm.com/" target="_blank" class="text-blue-500 hover:underline">Git</a> 和 <a href="https://nodejs.org/" target="_blank" class="text-blue-500 hover:underline">Node.js</a>。然后，在终端中运行以下命令来安装SHOPLINE CLI。支持npm和yarn两种安装方式：</p>
                            <div class="bg-gray-800 text-white rounded-lg p-4 font-mono text-sm relative mb-2">
                                <span class="select-none">$ </span>npm install --global @shoplineos/cli
                                <button class="copy-btn absolute top-3 right-3 bg-gray-600 hover:bg-gray-500 text-white p-1 rounded-md text-xs">复制</button>
                            </div>
                            <div class="bg-gray-800 text-white rounded-lg p-4 font-mono text-sm relative">
                                <span class="select-none">$ </span>yarn global add @shoplineos/cli
                                <button class="copy-btn absolute top-3 right-3 bg-gray-600 hover:bg-gray-500 text-white p-1 rounded-md text-xs">复制</button>
                            </div>
                            <p class="text-gray-600 mt-4 text-sm">💡 可以使用以下命令检查是否安装成功和查看当前版本号：</p>
                            <div class="bg-gray-800 text-white rounded-lg p-4 font-mono text-sm relative mt-2">
                                <span class="select-none">$ </span>sl --version
                                <button class="copy-btn absolute top-3 right-3 bg-gray-600 hover:bg-gray-500 text-white p-1 rounded-md text-xs">复制</button>
                            </div>
                        </div>
                    </li>
                    <li class="flex items-start space-x-4">
                        <div class="flex-shrink-0 w-12 h-12 bg-blue-500 text-white rounded-full flex items-center justify-center text-xl font-bold">2</div>
                        <div>
                            <h3 class="text-xl font-semibold mb-2">登录店铺</h3>
                            <p class="text-gray-600 mb-4">将CLI工具与您的SHOPLINE店铺关联。将下方命令中的 `example.myshopline.com` 替换为您的店铺域名。</p>
                             <div class="bg-gray-800 text-white rounded-lg p-4 font-mono text-sm relative">
                                <span class="select-none">$ </span>sl login --store example.myshopline.com
                                <button class="copy-btn absolute top-3 right-3 bg-gray-600 hover:bg-gray-500 text-white p-1 rounded-md text-xs">复制</button>
                            </div>
                            <p class="text-gray-600 mt-4 text-sm">💡 此命令会在弹出的浏览器窗口中要求您登录账号。</p>
                        </div>
                    </li>
                    <li class="flex items-start space-x-4">
                        <div class="flex-shrink-0 w-12 h-12 bg-blue-500 text-white rounded-full flex items-center justify-center text-xl font-bold">3</div>
                        <div>
                            <h3 class="text-xl font-semibold mb-2">开始开发！</h3>
                            <p class="text-gray-600 mb-4">一切就绪！现在您可以创建新项目或拉取现有主题，开始您的开发之旅。</p>
                        </div>
                    </li>
                </ol>
            </div>
        </section>

        <!-- Core Commands Section -->
        <section id="commands" class="mb-16 md:mb-24 section-fade-in" aria-labelledby="commands-title">
            <h2 id="commands-title" class="text-3xl md:text-4xl font-bold text-center mb-2">核心命令浏览器</h2>
            <p class="text-lg text-gray-600 text-center mb-8 md:mb-12">点击切换，探索最常用的CLI命令。</p>
            <div class="max-w-7xl mx-auto bg-white p-4 sm:p-8 rounded-xl shadow-lg">
                <div class="intro-paragraph mb-8">
                     <p class="text-gray-600">SHOPLINE CLI提供了一套简洁而强大的命令来管理您的主题开发周期。下面的浏览器将带您了解每个核心命令的功能、用法以及它在开发流程中扮演的角色。通过互动式地探索这些命令，您可以快速建立起对CLI工作方式的直观理解。</p>
                </div>
                <div class="flex flex-col md:flex-row">
                    <div class="flex md:flex-col border-b-2 md:border-b-0 md:border-r-2 border-gray-200 mb-4 md:mb-0 md:pr-6 space-x-1 md:space-x-0 md:space-y-1 overflow-x-auto pb-2 md:pb-0">
                        <button class="command-tab flex-shrink-0 text-left w-full p-3 rounded-lg font-medium active" data-target="cmd-init">init</button>
                        <button class="command-tab flex-shrink-0 text-left w-full p-3 rounded-lg font-medium" data-target="cmd-serve">serve</button>
                        <button class="command-tab flex-shrink-0 text-left w-full p-3 rounded-lg font-medium" data-target="cmd-push">push</button>
                        <button class="command-tab flex-shrink-0 text-left w-full p-3 rounded-lg font-medium" data-target="cmd-pull">pull</button>
                        <button class="command-tab flex-shrink-0 text-left w-full p-3 rounded-lg font-medium" data-target="cmd-check">check</button>
                        <button class="command-tab flex-shrink-0 text-left w-full p-3 rounded-lg font-medium" data-target="cmd-package">package</button>
                    </div>
                    <div class="flex-grow md:pl-6">
                        <div id="cmd-init" class="command-content">
                            <h3 class="text-2xl font-bold mb-4">sl theme init</h3>
                            <div class="flex items-center bg-blue-50 p-4 rounded-lg mb-4">
                                <div class="text-4xl mr-4" role="img" aria-label="香槟瓶图标">🍾</div>
                                <p class="font-semibold text-blue-800">初始化主题，将从 Github 拉取 Bottle 主题到本地计算机。</p>
                            </div>
                            <p class="text-gray-600 mb-4">此命令会从我们的开放 Git 仓库中，克隆 Bottle 示例主题代码，并创建其副本。执行后会在当前目录创建新文件夹，您输入的主题名称会作为目录名称。</p>
                            <div class="bg-gray-800 text-white rounded-lg p-4 font-mono text-sm relative">
                                <span class="select-none">$ </span>sl theme init &lt;THEME_NAME&gt;
                                <button class="copy-btn absolute top-3 right-3 bg-gray-600 hover:bg-gray-500 text-white p-1 rounded-md text-xs">复制</button>
                            </div>
                            <div class="mt-4 p-3 bg-blue-50 rounded-lg">
                                <p class="text-sm text-blue-800"><strong>💡 可选参数：</strong></p>
                                <ul class="text-sm text-blue-700 mt-1 space-y-1">
                                    <li>• <code class="bg-blue-200 px-1 rounded">--clone-url &lt;URL&gt;</code> 指定Git仓库URL</li>
                                    <li>• <code class="bg-blue-200 px-1 rounded">--path &lt;PATH&gt;</code> 指定主题目录工作路径</li>
                                </ul>
                            </div>
                        </div>
                        <div id="cmd-serve" class="command-content hidden">
                            <h3 class="text-2xl font-bold mb-4">sl theme serve</h3>
                            <div class="flex items-center bg-green-50 p-4 rounded-lg mb-4">
                                <div class="text-4xl mr-4" role="img" aria-label="循环刷新图标">🔄</div>
                                <p class="font-semibold text-green-800">启动本地开发服务器，并创建开发中主题进行实时预览。</p>
                            </div>
                            <p class="text-gray-600 mb-4">这是开发中最核心的命令。会创建一个开发中主题（连接到用于开发的 SHOPLINE 商店的临时隐藏主题），在更改主题文件内容时，会实时刷新主题预览链接的内容。</p>
                            <div class="bg-gray-800 text-white rounded-lg p-4 font-mono text-sm relative">
                                <span class="select-none">$ </span>sl theme serve
                                <button class="copy-btn absolute top-3 right-3 bg-gray-600 hover:bg-gray-500 text-white p-1 rounded-md text-xs">复制</button>
                            </div>
                            <div class="mt-4 p-3 bg-green-50 rounded-lg">
                                <p class="text-sm text-green-800"><strong>💡 关于开发中主题：</strong></p>
                                <ul class="text-sm text-green-700 mt-2 space-y-1">
                                    <li>• 开发中主题不计入您的主题限制</li>
                                    <li>• 会在7天不活跃后从商店中删除</li>
                                    <li>• 运行 <code class="bg-green-200 px-1 rounded">sl logout</code> 命令时会删除开发中主题</li>
                                    <li>• 可用于实时查看本地开发的主题更改</li>
                                    <li>• 支持使用商家后台的主题编辑器与主题进行交互</li>
                                </ul>
                            </div>
                            <div class="mt-4 p-3 bg-yellow-50 rounded-lg">
                                <p class="text-sm text-yellow-800"><strong>⚠️ 可选参数：</strong></p>
                                <ul class="text-sm text-yellow-700 mt-1 space-y-1">
                                    <li>• <code class="bg-yellow-200 px-1 rounded">--theme &lt;THEME_ID&gt;</code> 连接到指定的主题</li>
                                    <li>• <code class="bg-yellow-200 px-1 rounded">--path &lt;PATH&gt;</code> 您的主题目录工作路径</li>
                                    <li>• <code class="bg-yellow-200 px-1 rounded">--environment &lt;ENV_NAME&gt;</code> 您想使用的环境</li>
                                </ul>
                            </div>
                        </div>
                                                 <div id="cmd-push" class="command-content hidden">
                             <h3 class="text-2xl font-bold mb-4">sl theme push</h3>
                            <div class="flex items-center bg-purple-50 p-4 rounded-lg mb-4">
                                <div class="text-4xl mr-4" role="img" aria-label="上传图标">📤</div>
                                <p class="font-semibold text-purple-800">上传本地的主题文件到 SHOPLINE，覆盖远程的版本。</p>
                            </div>
                            <p class="text-gray-600 mb-4">使用此命令上传本地的主题文件到 SHOPLINE，覆盖远程的版本。使用 <code class="bg-gray-200 px-1 rounded">--theme</code> 参数将主题上传到远程，并覆盖指定主题。</p>
                            <div class="bg-gray-800 text-white rounded-lg p-4 font-mono text-sm relative">
                                <span class="select-none">$ </span>sl theme push --theme &lt;THEME_ID&gt;
                                <button class="copy-btn absolute top-3 right-3 bg-gray-600 hover:bg-gray-500 text-white p-1 rounded-md text-xs">复制</button>
                            </div>
                            <div class="mt-4 p-3 bg-purple-50 rounded-lg">
                                <p class="text-sm text-purple-800"><strong>💡 可选参数：</strong></p>
                                <ul class="text-sm text-purple-700 mt-1 space-y-1">
                                    <li>• <code class="bg-purple-200 px-1 rounded">--theme &lt;THEME_ID&gt;</code> 指定要推送到的主题ID</li>
                                    <li>• <code class="bg-purple-200 px-1 rounded">--path &lt;PATH&gt;</code> 您的主题目录工作路径</li>
                                    <li>• <code class="bg-purple-200 px-1 rounded">--environment &lt;ENV_NAME&gt;</code> 您想使用的环境</li>
                                    <li>• <code class="bg-purple-200 px-1 rounded">--json</code> 以JSON格式输出</li>
                                    <li>• <code class="bg-purple-200 px-1 rounded">--allow-live</code> 允许推送到已发布的主题</li>
                                </ul>
                            </div>
                        </div>
                         <div id="cmd-pull" class="command-content hidden">
                             <h3 class="text-2xl font-bold mb-4">sl theme pull</h3>
                            <div class="flex items-center bg-yellow-50 p-4 rounded-lg mb-4">
                                <div class="text-4xl mr-4" role="img" aria-label="下载图标">📥</div>
                                <p class="font-semibold text-yellow-800">在本地下载线上已经存在的主题。</p>
                            </div>
                            <p class="text-gray-600 mb-4">此命令会提示您需要从列出的店铺下所有主题列表中选择要下载的主题，使用 <code class="bg-gray-200 px-1 rounded">--theme</code> 参数指定要下载的主题到本地。</p>
                            <div class="bg-gray-800 text-white rounded-lg p-4 font-mono text-sm relative">
                                <span class="select-none">$ </span>sl theme pull --theme &lt;THEME_ID&gt;
                                <button class="copy-btn absolute top-3 right-3 bg-gray-600 hover:bg-gray-500 text-white p-1 rounded-md text-xs">复制</button>
                            </div>
                            <div class="mt-4 p-3 bg-yellow-50 rounded-lg">
                                <p class="text-sm text-yellow-800"><strong>💡 可选参数：</strong></p>
                                <ul class="text-sm text-yellow-700 mt-1 space-y-1">
                                    <li>• <code class="bg-yellow-200 px-1 rounded">--theme &lt;THEME_ID&gt;</code> 指定要下载的主题ID</li>
                                    <li>• <code class="bg-yellow-200 px-1 rounded">--path &lt;PATH&gt;</code> 您的主题目录工作路径</li>
                                    <li>• <code class="bg-yellow-200 px-1 rounded">--environment &lt;ENV_NAME&gt;</code> 您想使用的环境</li>
                                    <li>• <code class="bg-yellow-200 px-1 rounded">--development</code> 拉取开发中主题</li>
                                    <li>• <code class="bg-yellow-200 px-1 rounded">--live</code> 拉取已发布的主题</li>
                                </ul>
                            </div>
                            <div class="mt-4 p-3 bg-blue-50 rounded-lg">
                                <p class="text-sm text-blue-800"><strong>⚠️ 注意：</strong> 运行此命令前需要您登录到店铺后进行操作。</p>
                            </div>
                        </div>
                        <div id="cmd-check" class="command-content hidden">
                             <h3 class="text-2xl font-bold mb-4">sl theme check</h3>
                            <div class="flex items-center bg-indigo-50 p-4 rounded-lg mb-4">
                                <div class="text-4xl mr-4" role="img" aria-label="显微镜检查图标">🔬</div>
                                <p class="font-semibold text-indigo-800">对主题代码进行静态分析，检查是否存在问题。</p>
                            </div>
                            <p class="text-gray-600 mb-4">这是一个非常重要的代码质量检查工具。它会根据SHOPLINE官方的最佳实践，检查代码中的错误、性能问题和可访问性问题，是上架前的必备步骤。</p>
                            <div class="bg-gray-800 text-white rounded-lg p-4 font-mono text-sm relative">
                                <span class="select-none">$ </span>sl theme check
                                <button class="copy-btn absolute top-3 right-3 bg-gray-600 hover:bg-gray-500 text-white p-1 rounded-md text-xs">复制</button>
                            </div>
                            <div class="mt-4 p-3 bg-indigo-50 rounded-lg">
                                <p class="text-sm text-indigo-800"><strong>💡 可选参数：</strong></p>
                                <ul class="text-sm text-indigo-700 mt-1 space-y-1">
                                    <li>• <code class="bg-indigo-200 px-1 rounded">--path &lt;PATH&gt;</code> 您的主题目录工作路径</li>
                                    <li>• <code class="bg-indigo-200 px-1 rounded">--environment &lt;ENV_NAME&gt;</code> 您想使用的环境</li>
                                    <li>• <code class="bg-indigo-200 px-1 rounded">--auto-correct</code> 自动修复可修复的问题</li>
                                </ul>
                            </div>
                        </div>
                        <div id="cmd-package" class="command-content hidden">
                             <h3 class="text-2xl font-bold mb-4">sl theme package</h3>
                            <div class="flex items-center bg-red-50 p-4 rounded-lg mb-4">
                                <div class="text-4xl mr-4" role="img" aria-label="打包图标">📦</div>
                                <p class="font-semibold text-red-800">将本地主题文件打包成可以上传到 SHOPLINE 店铺的 zip 文件。</p>
                            </div>
                            <p class="text-gray-600 mb-4">zip 文件中仅包含与默认 SHOPLINE 主题目录结构匹配的文件夹。ZIP 文件名使用 <code class="bg-gray-200 px-1 rounded">theme_name-theme_version.zip</code> 根据 <code class="bg-gray-200 px-1 rounded">theme.schema.json</code> 文件中使用的参数生成。</p>
                            <div class="bg-gray-800 text-white rounded-lg p-4 font-mono text-sm relative">
                                <span class="select-none">$ </span>sl theme package
                                <button class="copy-btn absolute top-3 right-3 bg-gray-600 hover:bg-gray-500 text-white p-1 rounded-md text-xs">复制</button>
                            </div>
                            <div class="mt-4 p-3 bg-red-50 rounded-lg">
                                <p class="text-sm text-red-800"><strong>💡 可选参数：</strong></p>
                                <ul class="text-sm text-red-700 mt-1 space-y-1">
                                    <li>• <code class="bg-red-200 px-1 rounded">--path &lt;PATH&gt;</code> 您的主题目录工作路径</li>
                                </ul>
                            </div>
                            <div class="mt-4 p-3 bg-blue-50 rounded-lg">
                                <p class="text-sm text-blue-800"><strong>📋 关于主题目录结构：</strong></p>
                                <p class="text-sm text-blue-700 mt-1">只有符合标准SHOPLINE主题目录结构的文件才会被打包。标准结构包括：blocks、components、layout、public、sections、i18n、templates、theme.config.json、theme.schema.json。</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Directory Structure Section -->
        <section id="directory-structure" class="mb-16 md:mb-24 section-fade-in" aria-labelledby="directory-title">
            <h2 id="directory-title" class="text-3xl md:text-4xl font-bold text-center mb-8 md:mb-12">SHOPLINE 主题目录结构</h2>
            <div class="max-w-7xl mx-auto bg-white p-8 rounded-xl shadow-lg">
                <div class="intro-paragraph mb-8">
                    <p class="text-gray-600">当您使用的目录与默认 SHOPLINE 主题目录结构匹配时，您才能运行某些主题命令，例如：<code class="bg-gray-200 px-1 rounded">sl theme serve</code>。此目录结构表示该主题无需构建步骤，或者已经完成了所有必需的文件转换。</p>
                    <div class="mt-4 p-4 bg-yellow-50 rounded-lg">
                        <p class="text-sm text-yellow-800"><strong>⚠️ 注意：</strong> 如果您使用构建工具来生成主题文件，那么您可能需要在存储生成文件的目录运行主题相关命令。</p>
                    </div>
                </div>
                
                <div class="bg-gray-900 text-green-400 p-6 rounded-lg font-mono text-sm">
                    <div class="mb-2">└── project</div>
                    <div class="ml-8 space-y-1">
                        <div>├── blocks/</div>
                        <div>├── components/</div>
                        <div>├── layout/</div>
                        <div>├── public/</div>
                        <div>├── sections/</div>
                        <div>├── i18n/</div>
                        <div>├── templates/</div>
                        <div>├── theme.config.json</div>
                        <div>└── theme.schema.json</div>
                    </div>
                </div>
                
                <div class="mt-8 grid md:grid-cols-2 gap-6">
                    <div class="space-y-4">
                        <h4 class="font-semibold text-lg text-gray-800">核心文件夹</h4>
                        <div class="space-y-3">
                            <div>
                                <h5 class="font-medium text-gray-700">blocks/</h5>
                                <p class="text-sm text-gray-600">可重用的主题组件块</p>
                            </div>
                            <div>
                                <h5 class="font-medium text-gray-700">components/</h5>
                                <p class="text-sm text-gray-600">小型可重用组件</p>
                            </div>
                            <div>
                                <h5 class="font-medium text-gray-700">layout/</h5>
                                <p class="text-sm text-gray-600">页面布局模板</p>
                            </div>
                            <div>
                                <h5 class="font-medium text-gray-700">public/</h5>
                                <p class="text-sm text-gray-600">静态资源文件（CSS、JS、图片）</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="space-y-4">
                        <h4 class="font-semibold text-lg text-gray-800">内容与配置</h4>
                        <div class="space-y-3">
                            <div>
                                <h5 class="font-medium text-gray-700">sections/</h5>
                                <p class="text-sm text-gray-600">页面区域模板</p>
                            </div>
                            <div>
                                <h5 class="font-medium text-gray-700">i18n/</h5>
                                <p class="text-sm text-gray-600">国际化语言文件</p>
                            </div>
                            <div>
                                <h5 class="font-medium text-gray-700">templates/</h5>
                                <p class="text-sm text-gray-600">页面模板文件</p>
                            </div>
                            <div>
                                <h5 class="font-medium text-gray-700">theme.config.json</h5>
                                <p class="text-sm text-gray-600">主题配置文件</p>
                            </div>
                            <div>
                                <h5 class="font-medium text-gray-700">theme.schema.json</h5>
                                <p class="text-sm text-gray-600">主题结构定义文件</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="mt-8 p-4 bg-blue-50 rounded-lg">
                    <h4 class="font-semibold text-blue-800 mb-2">💡 升级和卸载 CLI</h4>
                    <div class="text-sm text-blue-700 space-y-2">
                        <div><strong>升级 CLI：</strong></div>
                        <div class="bg-blue-100 p-2 rounded font-mono text-xs">
                            npm install --global @shoplineos/cli<br/>
                            # 或者<br/>
                            yarn global add @shoplineos/cli
                        </div>
                        <div class="mt-2"><strong>卸载 CLI：</strong></div>
                        <div class="bg-blue-100 p-2 rounded font-mono text-xs">
                            npm uninstall --global @shoplineos/cli<br/>
                            # 或者<br/>
                            yarn global remove @shoplineos/cli
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Workflows Section -->
        <section id="workflows" class="mb-16 md:mb-24 section-fade-in" aria-labelledby="workflows-title">
            <h2 id="workflows-title" class="text-3xl md:text-4xl font-bold text-center mb-2">互动式开发工作流</h2>
            <p class="text-lg text-gray-600 text-center mb-8 md:mb-12">模拟真实开发场景，理解代码与配置如何同步。</p>
            <div class="max-w-7xl mx-auto bg-white p-4 sm:p-8 rounded-xl shadow-lg">
                <div class="intro-paragraph mb-8">
                     <p class="text-gray-600">理论知识需要通过实践来巩固。这个互动模拟器将向您展示在独立开发和团队协作两种场景下，如何结合使用CLI命令和Git版本控制。点击下方的步骤，查看每一步的详细说明和推荐命令，以掌握最高效、最不易出错的开发流程。</p>
                </div>
                <div class="flex justify-center mb-8">
                    <div class="bg-gray-200 p-1 rounded-full flex">
                        <button id="solo-workflow-btn" class="workflow-toggle-btn active px-6 py-2 rounded-full font-semibold">独立开发</button>
                        <button id="team-workflow-btn" class="workflow-toggle-btn px-6 py-2 rounded-full font-semibold">团队协作</button>
                    </div>
                </div>

                <div class="flex flex-col lg:flex-row gap-8">
                    <!-- Workflow Diagram -->
                    <div id="workflow-diagram" class="flex-grow lg:w-3/5">
                        <!-- Solo Workflow Diagram -->
                        <div id="solo-workflow-diagram" class="space-y-4">
                            <div class="workflow-step active border-2 rounded-lg p-4" data-step="solo-1"><strong>1. 项目启动:</strong> 创建或拉取主题</div>
                            <div class="text-center text-gray-400">↓</div>
                            <div class="workflow-step border-2 rounded-lg p-4" data-step="solo-2"><strong>2. 开发循环:</strong> 运行 `serve` 并编码</div>
                            <div class="text-center text-gray-400">↓</div>
                            <div class="workflow-step border-2 rounded-lg p-4" data-step="solo-3"><strong>3. 迭代与部署:</strong> 使用 `check` 和 `push`</div>
                        </div>
                        <!-- Team Workflow Diagram -->
                        <div id="team-workflow-diagram" class="hidden space-y-4">
                            <div class="workflow-step active border-2 rounded-lg p-4" data-step="team-1"><strong>1. 同步开始:</strong> 拉取代码和配置</div>
                             <div class="text-center text-gray-400">↓</div>
                            <div class="workflow-step border-2 rounded-lg p-4" data-step="team-2"><strong>2. 本地开发:</strong> 运行 `serve` 编码</div>
                             <div class="text-center text-gray-400">↓</div>
                            <div class="workflow-step border-2 rounded-lg p-4" data-step="team-3"><strong>3. 分享成果:</strong> `check`, `commit`, `push`</div>
                        </div>
                    </div>
                    <!-- Workflow Details -->
                    <div id="workflow-details" class="flex-grow lg:w-2/5 bg-gray-50 p-6 rounded-lg min-h-[250px]">
                        <div id="solo-1" class="workflow-step-content">
                            <h4 class="font-bold text-lg mb-2">步骤1: 项目启动</h4>
                            <p class="text-gray-600 mb-2">为新项目运行 `init`，或为现有项目运行 `pull`。完成后，使用Git进行版本控制。</p>
                            <code class="block bg-gray-200 p-2 rounded text-sm text-gray-800">sl theme init &lt;name&gt;</code>
                        </div>
                        <div id="solo-2" class="workflow-step-content hidden">
                             <h4 class="font-bold text-lg mb-2">步骤2: 开发循环</h4>
                            <p class="text-gray-600 mb-2">运行 `serve` 命令。您的任何代码保存后，都会在浏览器中自动刷新，实现所见即所得的开发体验。</p>
                            <code class="block bg-gray-200 p-2 rounded text-sm text-gray-800">sl theme serve</code>
                        </div>
                        <div id="solo-3" class="workflow-step-content hidden">
                             <h4 class="font-bold text-lg mb-2">步骤3: 迭代与部署</h4>
                            <p class="text-gray-600 mb-2">完成一个阶段的工作后，运行 `check` 进行自检。通过后，停止 `serve`，使用 `push` 将所有本地更改一次性上传到您的开发主题。</p>
                            <code class="block bg-gray-200 p-2 rounded text-sm text-gray-800 mb-2">sl theme check</code>
                            <code class="block bg-gray-200 p-2 rounded text-sm text-gray-800">sl theme push</code>
                        </div>

                        <div id="team-1" class="workflow-step-content hidden">
                             <h4 class="font-bold text-lg mb-2">步骤1: 同步开始 (关键!)</h4>
                            <p class="text-gray-600 mb-2">开始工作前，必须先从Git拉取队友的<strong class="text-blue-600">代码</strong>，再用CLI拉取后台的<strong class="text-blue-600">配置</strong>。</p>
                            <code class="block bg-gray-200 p-2 rounded text-sm text-gray-800 mb-2">git pull</code>
                            <code class="block bg-gray-200 p-2 rounded text-sm text-gray-800">sl theme pull</code>
                        </div>
                        <div id="team-2" class="workflow-step-content hidden">
                             <h4 class="font-bold text-lg mb-2">步骤2: 本地开发</h4>
                            <p class="text-gray-600 mb-2">同步完成后，运行 `serve` 命令，安全地开始您自己的开发工作，无需担心冲突。</p>
                            <code class="block bg-gray-200 p-2 rounded text-sm text-gray-800">sl theme serve</code>
                        </div>
                         <div id="team-3" class="workflow-step-content hidden">
                             <h4 class="font-bold text-lg mb-2">步骤3: 分享成果</h4>
                            <p class="text-gray-600 mb-2">完成功能后，先运行 `check` 自检。然后将您的代码提交并推送到中央Git仓库，以便团队其他成员可以拉取您的更新。</p>
                             <code class="block bg-gray-200 p-2 rounded text-sm text-gray-800 mb-2">sl theme check</code>
                            <code class="block bg-gray-200 p-2 rounded text-sm text-gray-800">git push origin &lt;branch&gt;</code>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Best Practices Section -->
        <section id="best-practices" class="section-fade-in" aria-labelledby="best-practices-title">
             <h2 id="best-practices-title" class="text-3xl md:text-4xl font-bold text-center mb-8 md:mb-12">开发最佳实践</h2>
             <div class="max-w-7xl mx-auto">
                 <div class="intro-paragraph mb-8 text-center">
                     <p class="text-gray-600">掌握了工具和流程后，遵循这些行业最佳实践将帮助您构建出更健壮、更高性能、更易于维护的SHOPLINE主题。这些原则不仅关乎代码质量，更直接影响最终的用户体验和店铺的商业表现。</p>
                 </div>
                <div class="grid md:grid-cols-3 gap-8">
                    <article class="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow">
                        <div class="text-3xl mb-4" role="img" aria-label="古典建筑图标">🏛️</div>
                        <h3 class="text-xl font-bold mb-2">拥抱模块化</h3>
                        <p class="text-gray-600">将UI和功能拆分为独立的Sections和Blocks。这不仅使代码更清晰，也极大地增强了商家在后台的自定义能力。</p>
                    </article>
                     <article class="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow">
                        <div class="text-3xl mb-4" role="img" aria-label="闪电性能图标">⚡️</div>
                        <h3 class="text-xl font-bold mb-2">性能至上</h3>
                        <p class="text-gray-600">优先使用CSS实现功能，对图片和JS进行懒加载或异步加载，利用SHOPLINE CDN，确保店铺秒开，提升用户体验和转化率。</p>
                    </article>
                     <article class="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow">
                        <div class="text-3xl mb-4" role="img" aria-label="绿叶Git图标">🌿</div>
                        <h3 class="text-xl font-bold mb-2">Git是必需品</h3>
                        <p class="text-gray-600">使用Git进行版本控制，采用清晰的分支策略（如功能分支）。这是保证代码安全、方便回滚和高效团队协作的基石。</p>
                    </article>
                </div>
                
                <div class="mt-12 p-6 bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl">
                    <h3 class="text-xl font-bold text-center mb-4">📚 更多资源</h3>
                    <div class="grid md:grid-cols-2 gap-6 text-sm">
                        <div>
                            <h4 class="font-semibold text-gray-800 mb-2">文档链接</h4>
                            <ul class="space-y-1 text-gray-600">
                                <li>• <a href="https://sline.dev" target="_blank" class="text-blue-600 hover:underline">Sline 模版引擎</a></li>
                                <li>• <a href="https://hbs2sline.sline.dev" target="_blank" class="text-blue-600 hover:underline">Handlebar to Sline 语法转换器</a></li>
                                <li>• <a style="display: none;" href="https://sline.dev/Shopify-Dawn-vs-Shopline-Bottle.html" target="_blank" class="text-blue-600 hover:underline">Shopify Dawn theme vs SHOPLINE Bottle theme 官方对比报告</a></li>
                            </ul>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-800 mb-2">重要提醒</h4>
                            <ul class="space-y-1 text-gray-600">
                                <li>• 大多数 CLI 命令需要互联网连接才能运行</li>
                                <li>• 开发中主题会在7天不活跃后自动删除</li>
                                <li>• 运行 <code class="bg-gray-200 px-1 rounded">sl logout</code> 会删除您的开发中主题</li>
                                <li>• 使用 <code class="bg-gray-200 px-1 rounded">sl [command] -h</code> 获取命令帮助</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>
    
    <footer class="bg-gray-800 text-white py-8 mt-16" role="contentinfo">
        <div class="container mx-auto max-w-2xl px-4 text-center">
            <p class="text-sm text-gray-500 mt-2">本页面基于对公开的 SHOPLINE 官方开发者文档和代码仓库的研究分析而制作，旨在帮助开发者更好地理解和使用 SHOPLINE 主题开发工具。由于 SHOPLINE 的文档和工具可能会更新或变更，因此本页面内容可能会有所不同。请注意，本页面不代表 SHOPLINE 官方立场，仅供学习和参考之用。</p>
            <p><a href="https://sline.dev/disclaimer.html" target="_blank" class="text-blue-400 hover:underline">免责声明</a></p>
        </div>
    </footer>
    
         <div id="copy-notification" class="fixed bottom-5 right-5 bg-gray-800 text-white px-4 py-2 rounded-lg shadow-lg opacity-0 transition-opacity duration-300">
         已复制到剪贴板!
     </div>


<script>
document.addEventListener('DOMContentLoaded', () => {
    // Mobile Menu
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');
    mobileMenuButton.addEventListener('click', () => {
        mobileMenu.classList.toggle('hidden');
    });
    
    // Smooth scrolling for nav links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            const targetElement = document.querySelector(targetId);
            if(targetElement) {
                targetElement.scrollIntoView({
                    behavior: 'smooth'
                });
            }
             if (mobileMenu.classList.contains('hidden') === false) {
                mobileMenu.classList.add('hidden');
            }
        });
    });

    // Copy to clipboard
    const copyNotification = document.getElementById('copy-notification');
    document.querySelectorAll('.copy-btn').forEach(button => {
        button.addEventListener('click', (e) => {
            const codeContainer = e.target.parentElement;
            const textToCopy = codeContainer.innerText.replace('复制', '').trim().replace(/^\$\s/gm, '');

            const textarea = document.createElement('textarea');
            textarea.value = textToCopy;
            document.body.appendChild(textarea);
            textarea.select();
            try {
                document.execCommand('copy');
                copyNotification.style.opacity = '1';
                setTimeout(() => {
                    copyNotification.style.opacity = '0';
                }, 2000);
            } catch (err) {
                console.error('Failed to copy text: ', err);
            }
            document.body.removeChild(textarea);
        });
    });

    // Command tabs
    const commandTabs = document.querySelectorAll('.command-tab');
    const commandContents = document.querySelectorAll('.command-content');
    commandTabs.forEach(tab => {
        tab.addEventListener('click', () => {
            commandTabs.forEach(t => t.classList.remove('active'));
            tab.classList.add('active');
            
            const targetId = tab.getAttribute('data-target');
            commandContents.forEach(content => {
                if (content.id === targetId) {
                    content.classList.remove('hidden');
                } else {
                    content.classList.add('hidden');
                }
            });
        });
    });

    // Workflow simulator
    const soloBtn = document.getElementById('solo-workflow-btn');
    const teamBtn = document.getElementById('team-workflow-btn');
    const soloDiagram = document.getElementById('solo-workflow-diagram');
    const teamDiagram = document.getElementById('team-workflow-diagram');
    const workflowSteps = document.querySelectorAll('.workflow-step');
    const stepContents = document.querySelectorAll('.workflow-step-content');

    function switchWorkflow(type) {
        if (type === 'solo') {
            soloBtn.classList.add('active');
            teamBtn.classList.remove('active');
            soloDiagram.classList.remove('hidden');
            teamDiagram.classList.add('hidden');
            updateStepDetails('solo-1');
        } else {
            teamBtn.classList.add('active');
            soloBtn.classList.remove('active');
            teamDiagram.classList.remove('hidden');
            soloDiagram.classList.add('hidden');
            updateStepDetails('team-1');
        }
    }
    
    function updateStepDetails(targetStepId) {
         workflowSteps.forEach(s => s.classList.remove('active'));
         const activeStep = document.querySelector(`.workflow-step[data-step="${targetStepId}"]`);
         if(activeStep) {
            activeStep.classList.add('active');
         }

        stepContents.forEach(content => {
            if (content.id === targetStepId) {
                content.classList.remove('hidden');
            } else {
                content.classList.add('hidden');
            }
        });
    }

    soloBtn.addEventListener('click', () => switchWorkflow('solo'));
    teamBtn.addEventListener('click', () => switchWorkflow('team'));

    workflowSteps.forEach(step => {
        step.addEventListener('click', () => {
            const targetStepId = step.getAttribute('data-step');
            updateStepDetails(targetStepId);
        });
    });

    // Section fade-in on scroll
    const sections = document.querySelectorAll('.section-fade-in');
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('visible');
            }
        });
    }, { threshold: 0.1 });

    sections.forEach(section => {
        observer.observe(section);
    });
    
    // Active nav link on scroll
    const navLinks = document.querySelectorAll('.nav-link');
    const headerHeight = document.getElementById('header').offsetHeight;
    const sectionObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                navLinks.forEach(link => {
                    link.classList.toggle('active', link.getAttribute('href').substring(1) === entry.target.id);
                });
            }
        });
    }, { rootMargin: `-${headerHeight}px 0px 0px 0px`, threshold: 0.3 });

    document.querySelectorAll('main section').forEach(section => {
        sectionObserver.observe(section);
    });

    // Initial state for workflow toggle
    switchWorkflow('solo');
});
</script>

</body>
</html>