<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <!-- 核心SEO标签 -->
    <title>电商主题模板引擎深度对比：Liquid vs Handlebars vs Sline | 三大模版引擎技术选型指南 - SlineDev</title>
    <meta name="description" content="深度解析Shopify Liquid、Handlebars、Shopline Sline三大电商主题模板引擎的技术架构、性能表现、语法特性和选型策略。基于真实项目代码分析，为电商开发团队提供权威的技术选型指导。">
    <meta name="keywords" content="Liquid模板引擎,Handlebars模板引擎,Sline模板引擎,电商主题模板引擎,Shopify主题开发,Shopline主题,模板引擎对比,电商主题技术选型,Ruby模板引擎,JavaScript模板引擎,Golang模板引擎">
    <meta name="author" content="SlineDev技术团队">
    <meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1">
    <meta name="language" content="zh-CN">
    <meta name="revisit-after" content="7 days">
    <meta name="theme-color" content="#0f172a">
    <link rel="canonical" href="https://slinedev.com/liquid-handlebars-sline-comparison">
    
    <!-- Favicon 系列 -->
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon.ico">
    
    <!-- Open Graph 标签 -->
    <meta property="og:type" content="article">
    <meta property="og:title" content="电商主题模板引擎深度对比：Liquid vs Handlebars vs Sline | 技术选型指南">
    <meta property="og:description" content="深度解析Shopify Liquid、Handlebars、Shopline Sline三大电商主题模板引擎的技术架构、性能表现、语法特性和选型策略。基于真实项目代码分析的权威技术指导。">
    <meta property="og:url" content="https://slinedev.com/liquid-handlebars-sline-comparison">
    <meta property="og:site_name" content="SlineDev">
    <meta property="og:image" content="https://slinedev.com/images/template-engines-comparison-og.png">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <meta property="og:image:alt" content="电商主题模板引擎对比图表：Liquid、Handlebars、Sline三大引擎技术特性雷达图">
    <meta property="og:locale" content="zh_CN">
    <meta property="article:author" content="SlineDev技术团队">
    <meta property="article:published_time" content="2025-01-01T00:00:00+08:00">
    <meta property="article:modified_time" content="2025-01-21T00:00:00+08:00">
    <meta property="article:section" content="技术分析">
    <meta property="article:tag" content="模板引擎">
    <meta property="article:tag" content="电商技术">
    <meta property="article:tag" content="Liquid">
    <meta property="article:tag" content="Handlebars">
    <meta property="article:tag" content="Sline">
    <meta property="article:tag" content="Shopify">
    <meta property="article:tag" content="Shopline">
    
    <!-- Twitter Cards -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="电商主题模板引擎深度对比：Liquid vs Handlebars vs Sline">
    <meta name="twitter:description" content="深度解析三大电商主题模板引擎的技术架构、性能表现和选型策略，为开发团队提供权威指导">
    <meta name="twitter:image" content="https://slinedev.com/images/template-engines-comparison-twitter.png">
    <meta name="twitter:image:alt" content="电商主题模板引擎技术对比分析图">
    
    <!-- 性能优化标签 -->
    <link rel="preconnect" href="https://cdn.tailwindcss.com" crossorigin>
    <link rel="preconnect" href="https://cdn.jsdelivr.net" crossorigin>
    <link rel="preconnect" href="https://rsms.me" crossorigin>
    <link rel="dns-prefetch" href="//fonts.googleapis.com">
    
    <!-- 移动应用相关 -->
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="模板引擎对比">
    <meta name="format-detection" content="telephone=no">
    
    <!-- Google AI Search 优化 -->
    <meta name="google-site-verification" content="your-verification-code">
    <meta name="robots" content="index, follow, max-snippet:300, max-image-preview:large, max-video-preview:-1">
    
    <!-- 页面特定预览控制 -->
    <meta name="description" content="深度解析Shopify Liquid、Handlebars、Shopline Sline三大电商主题模板引擎。涵盖技术架构对比、性能分析、语法特性、选型策略，基于真实项目代码验证，为电商开发团队提供权威技术指导。">
    
    <!-- 多语言支持 -->
    <link rel="alternate" hreflang="zh-CN" href="https://slinedev.com/liquid-handlebars-sline-comparison">
    <link rel="alternate" hreflang="en" href="https://slinedev.com/en/liquid-handlebars-sline-comparison">
    <link rel="alternate" hreflang="x-default" href="https://slinedev.com/liquid-handlebars-sline-comparison">
    
    <!-- 结构化数据 - 技术文章 -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "TechArticle",
      "headline": "电商主题模板引擎深度对比：Liquid vs Handlebars vs Sline | 三大引擎技术选型指南",
      "description": "深度解析Shopify Liquid、Handlebars、Shopline Sline三大电商主题模板引擎的技术架构、性能表现、语法特性和选型策略。基于真实项目代码分析，为电商开发团队提供权威的技术选型指导
      "image": "https://slinedev.com/images/template-engines-comparison-featured.png",
      "author": {
        "@type": "Organization",
        "name": "SlineDev技术团队",
        "url": "https://slinedev.com"
      },
      "publisher": {
        "@type": "Organization",
        "name": "SlineDev",
        "logo": {
          "@type": "ImageObject",
          "url": "https://slinedev.com/sline-logo.png",
          "width": 200,
          "height": 60
        }
      },
      "datePublished": "2025-01-01T00:00:00+08:00",
      "dateModified": "2025-01-21T00:00:00+08:00",
      "mainEntityOfPage": {
        "@type": "WebPage",
        "@id": "https://slinedev.com/liquid-handlebars-sline-comparison"
      },
      "dependencies": ["Ruby", "JavaScript", "Golang"],
      "operatingSystem": "Cross-platform",
      "programmingLanguage": ["Ruby", "JavaScript", "Go"],
      "about": [
        {
          "@type": "SoftwareApplication",
          "name": "Liquid Template Engine",
          "description": "Shopify开发的Ruby模板引擎",
          "programmingLanguage": "Ruby",
          "applicationCategory": "DeveloperApplication"
        },
        {
          "@type": "SoftwareApplication", 
          "name": "Handlebars Template Engine",
          "description": "JavaScript模板引擎",
          "programmingLanguage": "JavaScript",
          "applicationCategory": "DeveloperApplication"
        },
        {
          "@type": "SoftwareApplication",
          "name": "Sline Template Engine", 
          "description": "Shopline自研的Golang模板引擎",
          "programmingLanguage": "Go",
          "applicationCategory": "DeveloperApplication"
        }
      ],
      "mentions": [
        {
          "@type": "Organization",
          "name": "Shopify",
          "url": "https://shopify.com"
        },
        {
          "@type": "Organization", 
          "name": "Shopline",
          "url": "https://shopline.com"
        }
      ]
    }
    </script>
    
    <!-- 结构化数据 - FAQ -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "什么是Liquid模板引擎？",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Liquid是由Shopify开发的Ruby模板引擎，采用解释型架构，设计理念是安全沙箱和简洁语法。它使用双重分隔符设计，明确区分输出{{}}和逻辑{%%}，是Shopify生态系统的核心技术。"
          }
        },
        {
          "@type": "Question",
          "name": "Handlebars和Sline引擎有什么区别？",
          "acceptedAnswer": {
            "@type": "Answer", 
            "text": "Handlebars是JavaScript实现的模板引擎，采用编译+运行时架构，设计理念是逻辑分离和语义化模板。Sline是Shopline自研的Golang预编译型引擎，设计理念是高性能和组件化，支持真正的组件调用和作用域隔离。"
          }
        },
        {
          "@type": "Question",
          "name": "如何选择合适的电商模板引擎？",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "选择建议：1）追求最大生态与最低风险选择Liquid；2）利用JavaScript技能和标准化选择Handlebars；3）追求极致性能与现代架构选择Sline。需要考虑团队技能、项目规模、性能要求和技术风险承受能力。"
          }
        },
        {
          "@type": "Question", 
          "name": "三大模板引擎的性能表现如何？",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "性能排序：Sline（编译型，4倍提升）> Liquid（解释型，高度优化）> Handlebars（依赖实现）。Sline基于Golang编译型架构具有数量级性能优势，Liquid经过Shopify全球优化表现稳定，Handlebars性能取决于具体实现和优化程度。"
          }
        }
      ]
    }
    </script>
    
    <!-- 结构化数据 - 面包屑导航 -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "BreadcrumbList",
      "itemListElement": [
        {
          "@type": "ListItem",
          "position": 1,
          "name": "首页",
          "item": "https://slinedev.com/"
        },
        {
          "@type": "ListItem",
          "position": 2,
          "name": "技术分析",
          "item": "https://slinedev.com/tech-analysis/"
        },
        {
          "@type": "ListItem",
          "position": 3,
          "name": "模板引擎对比",
          "item": "https://slinedev.com/liquid-handlebars-sline-comparison"
        }
      ]
    }
    </script>

    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        @import url('https://rsms.me/inter/inter.css');
        html { font-family: 'Inter', sans-serif; scroll-behavior: smooth; }
        body {
            background-color: #f8fafc; /* slate-50 */
            color: #334155; /* slate-700 */
        }
        .tab-btn {
            transition: all 0.3s ease;
        }
        .tab-btn.active {
            background-color: #ffffff;
            color: #0f172a; /* slate-900 */
            box-shadow: 0 4px 14px 0 rgb(0 0 0 / 10%);
        }
        .accordion-header {
            transition: background-color 0.3s ease;
        }
        .accordion-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.6s ease-in-out, padding 0.6s ease-in-out;
            padding: 0 1.5rem;
        }
        .accordion-item.active .accordion-content {
            max-height: 1500px; /* Increased for more content */
            padding: 1.5rem;
        }
        .accordion-item.active .accordion-icon {
            transform: rotate(180deg);
        }
        .chart-container {
            position: relative;
            margin: auto;
            height: 50vh;
            width: 100%;
            max-width: 700px;
            max-height: 500px;
        }
    </style>
</head>
<body class="antialiased">

    <div class="container mx-auto p-4 sm:p-6 lg:p-8">

        <header class="text-center mb-10 md:mb-16" role="banner">
            <h1 class="text-3xl md:text-5xl font-bold text-slate-900 tracking-tight">电商主题模板引擎深度对比</h1>
            <p class="mt-4 text-lg text-slate-600 max-w-2xl mx-auto">Liquid vs. Handlebars vs. Sline | 三大模版引擎技术特性全面解析</p>
            <div class="mt-6 text-sm text-slate-500" role="group" aria-label="支持的模板引擎">
                <span class="inline-block px-3 py-1 bg-slate-100 rounded-full mr-2" aria-label="Liquid引擎基于Ruby语言">Liquid模版引擎 - Ruby</span>
                <span class="inline-block px-3 py-1 bg-slate-100 rounded-full mr-2" aria-label="Handlebars引擎基于JavaScript语言">Handlebars模版引擎 - JavaScript</span>
                <span class="inline-block px-3 py-1 bg-slate-100 rounded-full" aria-label="Sline引擎基于Golang语言">Sline模版引擎 - Golang</span>
            </div>
            <!-- 面包屑导航 -->
            <nav aria-label="面包屑导航" class="mt-4">
                <ol class="flex justify-center items-center text-sm text-slate-500">
                    <li><a href="/" class="hover:text-slate-700">首页</a></li>
                    <li class="mx-2">›</li>
                    <li><a href="/tech-analysis/" class="hover:text-slate-700">技术分析</a></li>
                    <li class="mx-2">›</li>
                    <li aria-current="page" class="text-slate-700 font-medium">模板引擎对比</li>
                </ol>
            </nav>
        </header>

        <nav class="flex justify-center items-center mb-10 md:mb-16 p-1.5 bg-slate-200 rounded-full max-w-xl mx-auto sticky top-4 z-10 backdrop-blur-sm bg-slate-200/80" role="tablist" aria-label="模板引擎对比导航">
            <button class="tab-btn flex-1 text-center py-2.5 px-4 rounded-full font-semibold text-sm md:text-base text-slate-600 active" data-tab="compare" role="tab" aria-selected="true" aria-controls="compare-panel">综合对比</button>
            <button class="tab-btn flex-1 text-center py-2.5 px-4 rounded-full font-semibold text-sm md:text-base text-slate-600" data-tab="liquid" role="tab" aria-selected="false" aria-controls="liquid-panel">Liquid</button>
            <button class="tab-btn flex-1 text-center py-2.5 px-4 rounded-full font-semibold text-sm md:text-base text-slate-600" data-tab="handlebars" role="tab" aria-selected="false" aria-controls="handlebars-panel">Handlebars</button>
            <button class="tab-btn flex-1 text-center py-2.5 px-4 rounded-full font-semibold text-sm md:text-base text-slate-600" data-tab="sline" role="tab" aria-selected="false" aria-controls="sline-panel">Sline</button>
        </nav>

        <!-- 目录导航 -->
        <aside class="mb-10 bg-white p-6 rounded-2xl shadow-sm border border-slate-200 lg:hidden" role="navigation" aria-label="页面目录">
            <h3 class="text-lg font-semibold text-slate-800 mb-4">📋 页面目录</h3>
            <nav>
                <ul class="space-y-2 text-sm">
                    <li><a href="#why-it-matters" class="text-slate-600 hover:text-slate-800 flex items-center"><span class="mr-2">🎯</span>为何关注技术细节</a></li>
                    <li><a href="#philosophy-section" class="text-slate-600 hover:text-slate-800 flex items-center"><span class="mr-2">🏗️</span>引擎架构与设计理念</a></li>
                    <li><a href="#radar-chart-section" class="text-slate-600 hover:text-slate-800 flex items-center"><span class="mr-2">📊</span>引擎能力雷达图</a></li>
                    <li><a href="#details-section" class="text-slate-600 hover:text-slate-800 flex items-center"><span class="mr-2">🔍</span>九大维度详细分析</a></li>
                    <li><a href="#recommendations-section" class="text-slate-600 hover:text-slate-800 flex items-center"><span class="mr-2">💡</span>引擎选型与战略建议</a></li>
                    <li><a href="#research-findings" class="text-slate-600 hover:text-slate-800 flex items-center"><span class="mr-2">✅</span>引擎实现验证结果</a></li>
                    <li><a href="#dawn-details" class="text-slate-600 hover:text-slate-800 flex items-center"><span class="mr-2">💎</span>Dawn主题深度解析</a></li>
                    <li><a href="#bottle-details" class="text-slate-600 hover:text-slate-800 flex items-center"><span class="mr-2">🚀</span>Bottle主题深度解析</a></li>
                </ul>
            </nav>
        </aside>

        <main id="main-content" role="main">
            
            <div id="compare-section-container" role="tabpanel" aria-labelledby="compare-tab">
                <section id="why-it-matters" class="mb-16 bg-white p-8 rounded-2xl shadow-sm border border-slate-200">
                    <h2 class="text-2xl md:text-3xl font-bold text-center mb-4 text-slate-800">为何关注技术细节？</h2>
                    <p class="text-center text-slate-500 mb-8 max-w-3xl mx-auto">模板引擎是电商平台的"渲染心脏"，其架构设计和语法特性深刻影响着<strong>性能表现、开发效率、扩展能力和技术生态</strong>。本报告将深入剖析<mark>Liquid、Handlebars、Sline三大模版引擎</mark>的核心差异，为您的技术选型提供全面指导。</p>
                    
                    <!-- 核心要点摘要 - 针对AI搜索优化 -->
                    <!-- <div class="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-lg mb-6 text-left max-w-4xl mx-auto" data-ai-summary="true">
                        <h3 class="font-semibold text-slate-800 mb-2">🔑 核心要点摘要</h3>
                        <ul class="text-sm text-slate-700 space-y-1">
                            <li><strong>Liquid引擎</strong>：Shopify开发的Ruby模板引擎，解释型架构，安全沙箱设计，拥有最大的电商生态系统</li>
                            <li><strong>Handlebars引擎</strong>：JavaScript模板引擎，编译+运行时架构，逻辑分离设计，开源标准化解决方案</li>
                            <li><strong>Sline引擎</strong>：Shopline自研的Golang模板引擎，预编译型架构，组件化设计，性能提升4倍</li>
                            <li><strong>选型建议</strong>：追求生态选Liquid，利用JS技能选Handlebars，追求性能选Sline</li>
                        </ul>
                    </div> -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 text-center text-sm">
                        <div class="bg-slate-50 p-4 rounded-lg">
                            <h3 class="font-bold text-slate-700">渲染性能</h3>
                            <p class="text-slate-600 mt-1">模版引擎的架构设计（解释型 vs. 编译型）和优化策略直接影响页面渲染速度和用户体验。</p>
                        </div>
                        <div class="bg-slate-50 p-4 rounded-lg">
                            <h3 class="font-bold text-slate-700">语法设计</h3>
                            <p class="text-slate-600 mt-1">模版引擎的语法风格和功能特性决定了代码的可读性、维护性和开发效率。</p>
                        </div>
                        <div class="bg-slate-50 p-4 rounded-lg">
                            <h3 class="font-bold text-slate-700">生态支持</h3>
                            <p class="text-slate-600 mt-1">模版引擎的社区活跃度、工具链完善程度和文档质量影响长期技术支持和人才获取。</p>
                        </div>
                    </div>
                </section>

                <section id="philosophy-section" class="mb-16">
                    <h2 class="text-2xl md:text-3xl font-bold text-center mb-4 text-slate-800">引擎架构与设计理念</h2>
                    <p class="text-center text-slate-500 mb-10 max-w-3xl mx-auto">每个模版引擎都有其独特的技术架构和设计哲学，从Ruby的简洁安全到JavaScript的灵活标准，再到Golang的高性能编译，这些选择决定了模版引擎的核心特性和适用场景。</p>
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                        <div class="bg-white rounded-2xl shadow-md p-8 border border-slate-200 transition-all duration-300 hover:shadow-indigo-100 hover:-translate-y-1">
                            <h3 class="text-2xl font-bold text-indigo-700 mb-6 flex items-center"><span class="text-3xl mr-3">💎</span>Liquid引擎</h3>
                            <div class="space-y-5 text-sm">
                                <div><h4 class="font-semibold text-slate-600 mb-1">技术架构</h4><p class="text-slate-500">Ruby实现，解释型引擎</p></div>
                                <div><h4 class="font-semibold text-slate-600 mb-1">设计理念</h4><p class="text-slate-500">安全沙箱，简洁语法</p></div>
                                <div><h4 class="font-semibold text-slate-600 mb-1">扩展机制</h4><p class="text-slate-500">内置过滤器和标签</p></div>
                            </div>
                        </div>
                        <div class="bg-white rounded-2xl shadow-md p-8 border border-slate-200 transition-all duration-300 hover:shadow-amber-100 hover:-translate-y-1">
                            <h3 class="text-2xl font-bold text-amber-700 mb-6 flex items-center"><span class="text-3xl mr-3">🌍</span>Handlebars引擎</h3>
                            <div class="space-y-5 text-sm">
                                <div><h4 class="font-semibold text-slate-600 mb-1">技术架构</h4><p class="text-slate-500">JavaScript实现，编译+运行时</p></div>
                                <div><h4 class="font-semibold text-slate-600 mb-1">设计理念</h4><p class="text-slate-500">逻辑分离，语义化模板</p></div>
                                <div><h4 class="font-semibold text-slate-600 mb-1">扩展机制</h4><p class="text-slate-500">自定义Helper函数</p></div>
                            </div>
                        </div>
                        <div class="bg-white rounded-2xl shadow-md p-8 border border-slate-200 transition-all duration-300 hover:shadow-teal-100 hover:-translate-y-1">
                            <h3 class="text-2xl font-bold text-teal-700 mb-6 flex items-center"><span class="text-3xl mr-3">🚀</span>Sline引擎</h3>
                            <div class="space-y-5 text-sm">
                                <div><h4 class="font-semibold text-slate-600 mb-1">技术架构</h4><p class="text-slate-500">Golang实现，预编译型</p></div>
                                <div><h4 class="font-semibold text-slate-600 mb-1">设计理念</h4><p class="text-slate-500">高性能，组件化</p></div>
                                <div><h4 class="font-semibold text-slate-600 mb-1">扩展机制</h4><p class="text-slate-500">内置标签和组件系统</p></div>
                            </div>
                        </div>
                    </div>
                </section>

                <section id="radar-chart-section" class="mb-16">
                    <h2 class="text-2xl md:text-3xl font-bold text-center mb-4 text-slate-800">引擎能力雷达图</h2>
                    <p class="text-center text-slate-500 mb-10 max-w-3xl mx-auto">基于报告分析，对各引擎在六个关键维度的表现进行1-5分量化评估（5分为最优），以提供直观的综合实力概览。此评估从开发者和技术决策者视角出发。</p>
                    <div class="chart-container w-full max-w-3xl mx-auto bg-white p-4 rounded-2xl shadow-md border border-slate-200" role="img" aria-label="模板引擎能力雷达图对比">
                        <canvas id="engineRadarChart" aria-label="显示Liquid、Handlebars、Sline三大模版引擎在性能潜力、灵活性、开发体验、生态成熟度、学习曲线、技术风险六个维度的评分对比"></canvas>
                    </div>
                </section>
            </div>

            <section id="details-section" class="mb-16">
                <h2 class="text-2xl md:text-3xl font-bold text-center mb-10 text-slate-800">九大维度详细分析</h2>
                <div id="accordion-container" class="space-y-4 mx-auto" role="region" aria-label="模板引擎详细分析手风琴面板">
                </div>
            </section>

            <section id="recommendations-section" class="mb-16">
                <h2 class="text-2xl md:text-3xl font-bold text-center mb-10 text-slate-800">模版引擎选型与战略建议</h2>
                <p class="text-center text-slate-500 mb-10 max-w-3xl mx-auto">选择模板引擎不仅是技术决策，更是战略选择。它关乎开发团队的技能匹配、项目的风险控制以及业务的长期发展潜力。以下建议旨在帮助您根据自身情况做出最明智的决策。</p>
                <div id="recommendations-container" class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                </div>
            </section>
        </main>

                 <!-- 新增：实际代码语法对比 -->
        <!-- <section id="syntax-comparison" class="mb-16 bg-white p-8 rounded-2xl shadow-sm border border-slate-200">
            <h2 class="text-2xl md:text-3xl font-bold text-center mb-8 text-slate-800">💻 三大引擎语法对比</h2>
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
                <div class="bg-indigo-50 p-6 rounded-xl border border-indigo-200">
                    <h3 class="text-xl font-bold text-indigo-700 mb-4">Liquid引擎</h3>
                    <pre class="text-sm text-slate-700 bg-white p-4 rounded-lg overflow-x-auto"><code>{% assign product_title = product.title %}
{{ product_title | upcase }}

{% for variant in product.variants %}
  {% if variant.available %}
    <option value="{{ variant.id }}">
      {{ variant.title }} - {{ variant.price | money }}
    </option>
  {% endif %}
{% endfor %}</code></pre>
                </div>
                <div class="bg-amber-50 p-6 rounded-xl border border-amber-200">
                    <h3 class="text-xl font-bold text-amber-700 mb-4">Handlebars引擎</h3>
                    <pre class="text-sm text-slate-700 bg-white p-4 rounded-lg overflow-x-auto"><code>{{#var product_title = product.title /}}
{{ product_title | upcase }}

{{#for variant in product.variants}}
  {{#if variant.available}}
    <option value="{{ variant.id }}">
      {{ variant.title }} - {{ variant.price | money }}
    </option>
  {{/if}}
{{/for}}</code></pre>
                </div>
                <div class="bg-teal-50 p-6 rounded-xl border border-teal-200">
                    <h3 class="text-xl font-bold text-teal-700 mb-4">Sline引擎</h3>
                    <pre class="text-sm text-slate-700 bg-white p-4 rounded-lg overflow-x-auto"><code>{{#var product_title = product.title /}}
{{ product_title | upcase() }}

{{#for variant in product.variants}}
  {{#if variant.available}}
    <option value="{{ variant.id }}">
      {{ variant.title }} - {{ variant.price | money_with_currency() }}
    </option>
  {{/if}}
{{/for}}

{{#component "product/price" product=product use_variant=true /}}

{{#image_tag
  src
  class="product-image"
  widths="375,540,720,900"
  sizes="100vw"
  loading="lazy"
  alt=product.title
/}}</code></pre>
                </div>
            </div>
            <div class="mt-8 p-6 bg-slate-50 rounded-xl">
                <h4 class="text-lg font-bold text-slate-800 mb-3">🔍 引擎语法特性对比</h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div>
                        <strong class="text-indigo-700">Liquid引擎：</strong>
                        <ul class="mt-2 space-y-1 text-slate-600">
                            <li>• 双重分隔符设计</li>
                            <li>• 安全沙箱模式</li>
                            <li>• 丰富的内置过滤器</li>
                            <li>• 简洁的控制流语法</li>
                        </ul>
                    </div>
                    <div>
                        <strong class="text-amber-700">Handlebars引擎：</strong>
                        <ul class="mt-2 space-y-1 text-slate-600">
                            <li>• 逻辑无关模板</li>
                            <li>• 强大的Helper系统</li>
                            <li>• 预编译优化支持</li>
                            <li>• JavaScript生态兼容</li>
                        </ul>
                    </div>
                    <div>
                        <strong class="text-teal-700">Sline引擎：</strong>
                        <ul class="mt-2 space-y-1 text-slate-600">
                            <li>• 现代化语法设计</li>
                            <li>• 原生组件化支持</li>
                            <li>• 编译时性能优化</li>
                            <li>• 强类型变量管理</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section> -->

         <!-- 新增：深度代码分析发现 -->
        <section id="research-findings" class="mb-16 bg-gradient-to-r from-amber-50 to-orange-50 p-8 rounded-2xl border border-amber-200">
            <h2 class="text-2xl md:text-3xl font-bold text-center mb-8 text-amber-900">📋 模版引擎实现验证结果</h2>
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mx-auto">
                <div class="bg-white p-6 rounded-xl shadow-sm border border-green-200">
                    <h3 class="text-xl font-bold text-green-700 mb-4">✅ Liquid模版引擎验证</h3>
                    <ul class="space-y-2 text-sm text-slate-600">
                        <li><strong>实现语言：</strong>Ruby ✓</li>
                        <li><strong>引擎类型：</strong>解释型</li>
                        <li><strong>语法验证：</strong>{% assign %}, {{ }}, 过滤器链</li>
                        <li><strong>安全特性：</strong>沙箱模式，受限语法</li>
                        <li><strong>性能特点：</strong>服务器端渲染优化</li>
                        <li><strong>验证来源：</strong>Shopify Dawn主题</li>
                    </ul>
                </div>
                <div class="bg-white p-6 rounded-xl shadow-sm border border-blue-200">
                    <h3 class="text-xl font-bold text-blue-700 mb-4">✅ Handlebars模版引擎验证</h3>
                    <ul class="space-y-2 text-sm text-slate-600">
                        <li><strong>实现语言：</strong>JavaScript ✓</li>
                        <li><strong>引擎类型：</strong>编译+运行时</li>
                        <li><strong>语法验证：</strong>{{#for}}, {{snippet}}, {{{html}}}</li>
                        <li><strong>扩展特性：</strong>自定义Helper，预编译</li>
                        <li><strong>性能特点：</strong>编译优化，客户端/服务端</li>
                        <li><strong>验证来源：</strong>handlebars-theme-seed</li>
                    </ul>
                </div>
                <div class="bg-white p-6 rounded-xl shadow-sm border border-purple-200">
                    <h3 class="text-xl font-bold text-purple-700 mb-4">✅ Sline模版引擎验证</h3>
                    <ul class="space-y-2 text-sm text-slate-600">
                        <li><strong>实现语言：</strong>Golang ✓</li>
                        <li><strong>引擎类型：</strong>预编译型</li>
                        <li><strong>语法验证：</strong>{{#var}}, {{#component}}, {{#image_tag}}</li>
                        <li><strong>核心特性：</strong>组件化，变量管理，内置标签</li>
                        <li><strong>性能特点：</strong>编译时优化，高性能渲染</li>
                        <li><strong>验证来源：</strong>shoplineos/Bottle主题</li>
                    </ul>
                </div>
            </div>
            <!-- <div class="mt-8 p-6 bg-white rounded-xl border border-slate-200">
                <h4 class="text-lg font-bold text-slate-800 mb-3">🔍 深度代码分析方法</h4>
                <p class="text-sm text-slate-600 leading-relaxed">
                    本报告基于以下方法进行分析：<strong>1) GitHub仓库研究</strong> - 深入分析shoplineos/Bottle官方仓库的最新代码和提交历史；
                    <strong>2) 语法对比</strong> - 通过grep搜索和语义分析对比三种引擎的语法特征；
                    <strong>3) 架构分析</strong> - 对比文件组织结构和组件化程度；
                    <strong>4) 技术验证</strong> - 基于真实代码验证技术特性和性能假设。
                    所有分析均基于实际可运行的主题代码，确保技术对比的准确性和实用性。Sline引擎确认为Shopline自研的Golang模板引擎。
                </p>
            </div> -->
        </section>

        <!-- 新增：Dawn主题详细信息部分 -->
        <section id="dawn-details" class="mb-16 bg-gradient-to-r from-indigo-50 to-blue-50 p-8 rounded-2xl border border-indigo-200">
            <h2 class="text-2xl md:text-3xl font-bold text-center mb-8 text-indigo-900">Dawn主题深度解析 (Liquid模版引擎)</h2>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mx-auto">
                <div class="bg-white p-6 rounded-xl shadow-sm">
                    <h3 class="text-xl font-bold text-indigo-700 mb-4">🏆 项目概况</h3>
                    <ul class="space-y-2 text-sm text-slate-600">
                        <li><strong>GitHub地址：</strong>Shopify/dawn</li>
                        <li><strong>社区热度：</strong>2.7k ⭐ | 3.9k 🍴</li>
                        <li><strong>定位：</strong>Shopify首个开源参考主题</li>
                        <li><strong>特色：</strong>HTML优先、JavaScript仅作增强</li>
                        <li><strong>性能：</strong>比Debut主题快35%</li>
                        <li><strong>架构：</strong>Online Store 2.0特性内置</li>
                    </ul>
                </div>
                <div class="bg-white p-6 rounded-xl shadow-sm">
                    <h3 class="text-xl font-bold text-indigo-700 mb-4">🛠️ 技术特征</h3>
                    <ul class="space-y-2 text-sm text-slate-600">
                        <li><strong>模板引擎：</strong>Liquid (Ruby-based)</li>
                        <li><strong>文件结构：</strong>assets, config, layout, sections, snippets, templates</li>
                        <li><strong>开发工具：</strong>Shopify CLI, Theme Check, GitHub Actions</li>
                        <li><strong>设计原则：</strong>Web-native, 渐进增强, 服务器渲染</li>
                        <li><strong>代码组成：</strong>Liquid 63.4%, CSS 24.0%, JavaScript 12.6%</li>
                        <li><strong>维护状态：</strong>活跃维护，定期更新</li>
                    </ul>
                </div>
                <div class="bg-white p-6 rounded-xl shadow-sm">
                    <h3 class="text-xl font-bold text-indigo-700 mb-4">💡 核心优势</h3>
                    <ul class="space-y-2 text-sm text-slate-600">
                        <li><strong>性能导向：</strong>轻量级、快速加载</li>
                        <li><strong>可访问性：</strong>语义化HTML、渐进增强</li>
                        <li><strong>开发友好：</strong>完整工具链、CI/CD集成</li>
                        <li><strong>主题商店：</strong>可作为起点但需实质性差异化</li>
                        <li><strong>最佳实践：</strong>代码质量、性能优化示范</li>
                        <li><strong>社区支持：</strong>活跃的开发者生态</li>
                    </ul>
                </div>
                <div class="bg-white p-6 rounded-xl shadow-sm">
                    <h3 class="text-xl font-bold text-indigo-700 mb-4">⚠️ 注意事项</h3>
                    <ul class="space-y-2 text-sm text-slate-600">
                        <li><strong>主分支风险：</strong>可能包含未发布功能</li>
                        <li><strong>稳定版本：</strong>建议使用Theme Store版本</li>
                        <li><strong>定制限制：</strong>需要深度理解Liquid语法</li>
                        <li><strong>平台绑定：</strong>专为Shopify生态设计</li>
                        <li><strong>学习曲线：</strong>需要掌握Shopify特有概念</li>
                        <li><strong>商业考量：</strong>主题商店提交需差异化</li>
                    </ul>
                </div>
            </div>
        </section>

        <!-- 新增：Bottle主题深度解析部分 -->
        <section id="bottle-details" class="mb-16 bg-gradient-to-r from-teal-50 to-green-50 p-8 rounded-2xl border border-teal-200">
            <h2 class="text-2xl md:text-3xl font-bold text-center mb-8 text-teal-900">Bottle主题深度解析 (Sline模版引擎)</h2>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mx-auto">
                <div class="bg-white p-6 rounded-xl shadow-sm">
                    <h3 class="text-xl font-bold text-teal-700 mb-4">🏆 项目概况</h3>
                    <ul class="space-y-2 text-sm text-slate-600">
                        <li><strong>GitHub地址：</strong>shoplineos/Bottle</li>
                        <li><strong>主题版本：</strong>1.0.2 (活跃维护)</li>
                        <li><strong>引擎版本：</strong>OS_3.0 (Shopline自研)</li>
                        <li><strong>技术栈：</strong>Golang模板引擎 + 现代组件架构</li>
                        <li><strong>特色：</strong>组件优先、预编译型、高性能</li>
                        <li><strong>架构：</strong>components/blocks/sections三层体系</li>
                    </ul>
                </div>
                <div class="bg-white p-6 rounded-xl shadow-sm">
                    <h3 class="text-xl font-bold text-teal-700 mb-4">🛠️ 技术特征</h3>
                    <ul class="space-y-2 text-sm text-slate-600">
                        <li><strong>模板引擎：</strong>Sline (Golang-based)</li>
                        <li><strong>文件结构：</strong>components, blocks, sections, layout, templates</li>
                        <li><strong>内置标签：</strong>{{#image_tag}}, {{#placeholder_svg}}, {{#content}}</li>
                        <li><strong>组件系统：</strong>{{#component}} 真组件调用</li>
                        <li><strong>变量管理：</strong>{{#var}} 声明 + {{#set}} 赋值</li>
                        <li><strong>维护状态：</strong>2025年6月最新提交，持续更新</li>
                    </ul>
                </div>
                <div class="bg-white p-6 rounded-xl shadow-sm">
                    <h3 class="text-xl font-bold text-teal-700 mb-4">💡 核心优势</h3>
                    <ul class="space-y-2 text-sm text-slate-600">
                        <li><strong>性能导向：</strong>Golang编译型引擎，渲染速度4倍提升</li>
                        <li><strong>组件化：</strong>150+高度模块化组件，真正的作用域隔离</li>
                        <li><strong>现代语法：</strong>接近现代编程语言的语法设计</li>
                        <li><strong>内置优化：</strong>图像懒加载、CSS变量、响应式支持</li>
                        <li><strong>多语言：</strong>内置i18n支持，10+语言包</li>
                        <li><strong>电商特化：</strong>完整的购物车、订单、用户系统</li>
                    </ul>
                </div>
                <div class="bg-white p-6 rounded-xl shadow-sm">
                    <h3 class="text-xl font-bold text-teal-700 mb-4">⚠️ 技术考量</h3>
                    <ul class="space-y-2 text-sm text-slate-600">
                        <li><strong>专有技术：</strong>完全依赖Shopline生态，技术锁定风险</li>
                        <li><strong>学习曲线：</strong>全新语法，需要团队重新学习</li>
                        <li><strong>社区支持：</strong>仅限官方文档，无第三方教程</li>
                        <li><strong>人才稀缺：</strong>掌握Sline的开发者极少</li>
                        <li><strong>调试困难：</strong>编译型引擎，错误定位相对复杂</li>
                        <li><strong>平台绑定：</strong>无法迁移到其他电商平台</li>
                    </ul>
                </div>
            </div>
            <!-- <div class="mt-8 p-6 bg-white rounded-xl border border-slate-200">
                <h4 class="text-lg font-bold text-slate-800 mb-3">🔍 代码架构发现</h4>
                <p class="text-sm text-slate-600 leading-relaxed">
                    通过对Bottle主题仓库的深度分析发现：<strong>1) 组件化程度极高</strong> - components目录包含cart、facets、product等20+个功能模块；
                    <strong>2) 语法现代化</strong> - 支持{{#capture}}、{{#switch}}等高级控制流，以及{{#image_tag}}等内置标签；
                    <strong>3) 性能优化</strong> - 内置图像懒加载、CSS变量生成、响应式断点管理；
                    <strong>4) 开发工具链</strong> - 完整的主题配置系统和多语言支持。
                    这确实是一个面向未来的电商主题引擎，但需要权衡技术先进性与生态风险。
                </p>
            </div> -->
        </section>

        <footer class="text-center mt-16 pt-8 border-t border-slate-200" role="contentinfo">
            <div class="max-w-4xl mx-auto">
                <div class="mb-6">
                    <h3 class="text-lg font-semibold text-slate-800 mb-3">关于本技术对比报告</h3>
                    <p class="text-sm text-slate-600 mb-2">本技术对比报告基于对真实项目代码的深度分析和引擎特性验证，为电商开发团队提供权威的技术选型指导。</p>
                    <p class="text-xs text-slate-500">分析截至2025年1月，所有引擎特性均基于实际代码验证。数据来源包括Shopify Dawn主题、Shopline Bottle主题等官方项目。</p>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6 text-sm">
                    <div class="bg-slate-50 p-3 rounded-lg">
                        <h4 class="font-semibold text-slate-700 mb-1">Liquid模版引擎</h4>
                        <p class="text-slate-600">Ruby实现 | 解释型架构</p>
                        <p class="text-slate-500 text-xs">Shopify生态核心技术</p>
                    </div>
                    <div class="bg-slate-50 p-3 rounded-lg">
                        <h4 class="font-semibold text-slate-700 mb-1">Handlebars模版引擎</h4>
                        <p class="text-slate-600">JavaScript实现 | 编译+运行时</p>
                        <p class="text-slate-500 text-xs">开源标准化解决方案</p>
                    </div>
                    <div class="bg-slate-50 p-3 rounded-lg">
                        <h4 class="font-semibold text-slate-700 mb-1">Sline模版引擎</h4>
                        <p class="text-slate-600">Golang实现 | 预编译型</p>
                        <p class="text-slate-500 text-xs">Shopline自研高性能引擎</p>
                    </div>
                </div>
                
                <div class="text-xs text-slate-400 space-y-1">
                    <p>© 2025 SlineDev. 专注于电商技术分析与解决方案。</p>
                    <p>关键词：电商模板引擎、技术选型、性能优化、开发效率、架构设计</p>
                    <p>联系我们：<a href="mailto:<EMAIL>" class="text-slate-600 hover:text-slate-800"><EMAIL></a></p>
                </div>
            </div>
        </footer>
    </div>

<script>
const DB = {
    radarData: {
        labels: ['性能潜力', '灵活性', '开发体验', '生态成熟度', '学习曲线', '技术风险'],
        datasets: [
            {
                label: 'Liquid',
                data: [4, 4, 5, 5, 4, 1],
                backgroundColor: 'rgba(79, 70, 229, 0.2)',
                borderColor: 'rgb(79, 70, 229)',
                pointBackgroundColor: 'rgb(79, 70, 229)',
            },
            {
                label: 'Handlebars',
                data: [3, 4.5, 4, 3.5, 5, 3],
                backgroundColor: 'rgba(180, 83, 9, 0.2)',
                borderColor: 'rgb(180, 83, 9)',
                pointBackgroundColor: 'rgb(180, 83, 9)',
            },
            {
                label: 'Sline',
                data: [5, 5, 3, 2, 2, 4],
                backgroundColor: 'rgba(13, 148, 136, 0.2)',
                borderColor: 'rgb(13, 148, 136)',
                pointBackgroundColor: 'rgb(13, 148, 136)',
            }
        ]
    },
    comparisonData: [
        {
            title: "语法设计 (Syntax Design)",
            icon: "✍️",
            content: {
                liquid: "<strong>双重分隔符，意图清晰</strong>: 明确区分输出 `{{ }}` 和逻辑 `{% %}`。提供 `case/when` 等强大控制流。<strong>强大的空白控制</strong>: 通过 `-` 移除渲染时多余的空格换行，利于生成整洁HTML。语法受限，旨在安全可控，复杂逻辑需通过过滤器链实现。",
                handlebars: "<strong>统一分隔符，JS友好</strong>: 输出和逻辑均使用 `{{ }}`。原生逻辑极简，但Shopline通过自定义助手注入了 `and/or` 等强大逻辑，使其\"Liquid化\"。<strong>HTML不转义</strong>使用 `{{{...}}}`，语法更简洁。对JS开发者非常直观。",
                sline: "<strong>全新专有语法，高度现代化</strong>: 采用 `{{#var name = value /}}` 进行变量声明，`{{#set var = value /}}` 进行赋值。<strong>组件化架构</strong>: `{{#component 'path' prop=value /}}` 实现真正的组件调用。<strong>管道操作符</strong>: 支持 `value | filter() | append()` 链式操作。语法更接近现代编程语言，但学习曲线陡峭。"
            }
        },
        {
            title: "核心功能与电商特性 (Core Features)",
            icon: "🎯",
            content: {
                liquid: "<strong>电商全栈解决方案</strong>: 内置购物车、结账、产品变体、库存管理、多语言、SEO优化等完整功能。<strong>丰富的过滤器生态</strong>: `money`, `asset_url`, `handleize` 等大量专用过滤器。<strong>成熟的主题生态</strong>: 拥有庞大的主题市场和开发者社区。",
                handlebars: "<strong>完整电商功能集</strong>: 通过200+模板文件实现购物车、用户中心、订单管理、支付流程。<strong>丰富的图标库</strong>: 内置100+个SVG图标组件。<strong>多语言支持</strong>: 完整的i18n体系和本地化功能。<strong>响应式设计</strong>: 移动端优先的现代UI组件。",
                sline: "<strong>下一代组件化电商</strong>: 150+个高度模块化组件，支持产品详情、购物车、用户系统等完整电商流程。<strong>高级变体管理</strong>: 支持多维度产品变体和复杂定价规则。<strong>性能优化</strong>: 编译时优化确保即使复杂功能也能保持高性能。<strong>现代化架构</strong>: 真正的组件作用域和props传递机制。"
            }
        },
        {
            title: "数据处理与语法特性 (Data Handling & Syntax)",
            icon: "⚙️",
            content: {
                liquid: "<strong>丰富的电商过滤器</strong>: 内置 `money`, `asset_url`, `handleize` 等大量专用过滤器。<strong>独特的真假值判断</strong>: 仅 `false` 和 `nil` 为假，空字符串和0为真，是常见陷阱。<strong>作用域简单</strong>: 不支持向上查找父级作用域。",
                handlebars: "<strong>依赖助手扩展</strong>: Shopline为其注入了与Liquid对等的全套电商助手。<strong>遵循JS真假值</strong>: `false`, `null`, `0`, `''` 均为假，更符合开发者直觉。<strong>支持父级作用域</strong>: 通过 `../` 路径段访问父级上下文，处理复杂数据结构更方便。",
                sline: "<strong>组件化与作用域隔离</strong>: 引入 `component` 标签，拥有独立作用域，数据需显式传入。这是最现代、最健壮的模块化方案，避免了上下文污染，提升了代码的可维护性和健壮性。<strong>管道操作符</strong>: 支持链式数据处理和函数调用。"
            }
        },
        {
            title: "技术特征与渲染模型 (Architecture & Rendering)",
            icon: "🛠️",
            content: {
                liquid: "<strong>SSR优先的解释型引擎</strong>: 由Ruby编写，在服务器端解释执行。设计哲学是\"HTML优先，JS仅为增强\"，确保客户端轻量、快速，SEO友好。<strong>安全沙箱</strong>: 无法访问文件系统或执行任意代码，保证平台安全。",
                handlebars: "<strong>基于JS的JIT/解释型引擎</strong>: 将模板编译为JS函数执行。可在服务器端（SSR）或客户端（动态更新）运行，灵活性高但有JS运行时开销。通过预编译可显著提升性能。<strong>文件结构</strong>: 采用sections/snippets/layout三层架构。",
                sline: "<strong>Golang自研编译型引擎</strong>: 基于Golang开发的高性能模板引擎，确认为OS_3.0版本。支持{{#image_tag}}、{{#placeholder_svg}}等内置标签，{{#content}}块级内容管理，以及真正的{{#component}}组件化架构。在部署时预编译为高效代码，性能远超解释型引擎。"
            }
        },
        {
            title: "性能 (Performance)",
            icon: "⚡️",
            content: {
                liquid: "<strong>高度优化，行业基准</strong>: Shopify全球CDN和服务器为Liquid渲染深度优化。性能稳定可靠，比前代快35%，是行业基准之一。通过`theme check`工具强制执行性能最佳实践。",
                handlebars: "<strong>依赖实现和优化</strong>: 性能优于纯客户端渲染，但逊于编译型引擎。Shopline公布其Lighthouse移动端90+分，表现卓越，体现了其对标准技术的深度优化能力。",
                sline: "<strong>极致性能，核心卖点</strong>: 官方宣称\"渲染速度最高提升4倍\"。从架构上看，编译型引擎相对JS引擎确有数量级的性能优势。这一性能飞跃是其支持\"五级嵌套\"等复杂功能的技术基石。"
            }
        },
        {
            title: "开发体验 (Developer Experience)",
            icon: "👨‍💻",
            content: {
                liquid: "<strong>成熟完善，工具链强大</strong>: 提供强大的Shopify CLI，集成本地开发、热重载、Theme Check代码检查、CI/CD集成。工作流清晰高效。VSCode插件和社区支持极其丰富。",
                handlebars: "<strong>对标且熟悉，生态借力</strong>: Shopline提供功能对等的CLI和VSCode插件。开发者可利用Handlebars的广泛社区知识和自身的JS经验，学习成本低。",
                sline: "<strong>初期挑战，依赖官方</strong>: 共享Shopline工具链，但作为新语言，面临文档细节不足、无社区案例、调试困难等早期挑战。知识完全依赖官方渠道，存在\"知识鸿沟\"。"
            }
        },
        {
            title: "灵活性 (Flexibility)",
            icon: "🚀",
            content: {
                liquid: "<strong>平台驱动的灵活性</strong>: 语言本身封闭，但通过强大的Metafields和\"万物皆区块\"架构，赋予商户极高的无代码定制能力。Sections可在任何页面复用。",
                handlebars: "<strong>语言与平台双重灵活性</strong>: 原生支持通过`registerHelper`自定义逻辑，是三者中语言层面最灵活的。平台层面同样支持Metafields和区块化架构。",
                sline: "<strong>架构驱动的极致灵活性</strong>: 语言封闭，但通过支持**\"五级嵌套块\"**的组件化架构，在页面布局和结构定制的灵活性上达到了新的高度，远超前两者，模糊了主题和页面构建器的界限。"
            }
        },
        {
            title: "扩展性 (Extensibility)",
            icon: "🧩",
            content: {
                liquid: "<strong>平台级扩展，安全可控</strong>: 开发者无法定义新标签/过滤器。扩展完全通过平台API、Metafields和主题应用扩展（App Blocks）实现。模式安全、可控，利于生态稳定。",
                handlebars: "<strong>语言级扩展，潜力巨大</strong>: 核心优势是可通过API注册自定义助手。但SaaS平台通常会限制此功能以保证安全。Shopline的模式更倾向于平台级扩展。",
                sline: "<strong>平台级扩展，模式统一</strong>: 专有编译型引擎，几乎不可能开放语言级扩展。扩展模型与Liquid一致，完全由平台（Shopline）掌控，保证生态稳定。"
            }
        },
        {
            title: "生态社区 (Ecosystem & Community)",
            icon: "🌍",
            content: {
                liquid: "<strong>全球最大、最活跃</strong>: 拥有庞大的全球开发者社区、合作伙伴网络、海量教程和开源贡献（Dawn主题）。Shopify App Store应用超9500+，资源极其丰富。",
                handlebars: "<strong>双重生态，借力开源</strong>: 同时受益于Shopline的官方生态和Handlebars作为流行开源项目自身的广泛社区资源。Shopline应用约120+，成长中。",
                sline: "<strong>孤岛生态，风险集中</strong>: 社区支持完全依赖Shopline官方。开发者无法求助于外部开源社区，存在技术支持风险和人才库规模限制，可能推高开发成本。"
            }
        },
        {
            title: "使用场景 (Usage Scenarios)",
            icon: "🎯",
            content: {
                liquid: "<strong>追求稳定与生态的项目</strong>: 适合需要利用最庞大App生态、追求低技术风险、开发资源充足、目标为全球市场的项目。是大型企业和初创公司的首选。",
                handlebars: "<strong>平衡标准与定制的项目</strong>: 适合希望利用标准化、广泛流行的JS模板技术，同时又能受益于平台深度电商功能集成的团队。基于200+模板文件的完整实现，适合中型电商项目。",
                sline: "<strong>追求极致性能与现代架构的项目</strong>: 基于真实Bottle主题验证，拥有150+组件的成熟架构和强大的技术实力。适合追求顶尖性能、现代化开发体验，并愿意深度融入Shopline生态的团队。"
            }
        }
    ],
    recommendations: [
        {
            title: "寻求最大生态与最低风险",
            engine: "liquid",
            description: "选择Liquid，本质上是选择Shopify庞大、成熟且稳定的生态系统。对于希望快速启动、利用海量应用、并将技术风险降至最低的项目，这是最安全可靠的选择。",
            color: 'indigo'
        },
        {
            title: "利用标准化JS技能",
            engine: "handlebars",
            description: "对于拥有深厚JavaScript背景的开发团队，选择Handlebars可以最大限度地利用现有技能。这是一个平衡了开源标准、开发效率和平台功能的务实选择。",
            color: 'amber'
        },
        {
            title: "选择极致性能与现代架构",
            engine: "sline",
            description: "基于真实Bottle主题代码分析，Sline展现出强大的技术实力和现代化架构。选择Sline适合追求极致性能、拥抱组件化开发、并愿意投资于Shopline生态的团队。",
            color: 'teal'
        }
    ]
};

document.addEventListener('DOMContentLoaded', () => {
    const tabButtons = document.querySelectorAll('.tab-btn');
    const accordionContainer = document.getElementById('accordion-container');
    const recommendationsContainer = document.getElementById('recommendations-container');
    const compareSectionContainer = document.getElementById('compare-section-container');
    const detailsSection = document.getElementById('details-section');
    const recommendationsSection = document.getElementById('recommendations-section');
    let radarChart;

    const engineColors = {
        liquid: { text: 'text-indigo-700', border: 'border-indigo-200', bg: 'bg-indigo-50' },
        handlebars: { text: 'text-amber-700', border: 'border-amber-200', bg: 'bg-amber-50' },
        sline: { text: 'text-teal-700', border: 'border-teal-200', bg: 'bg-teal-50' }
    };

    function createAccordion(filter = 'all') {
        accordionContainer.innerHTML = '';
        DB.comparisonData.forEach((item) => {
            const accordionItem = document.createElement('div');
            accordionItem.className = 'accordion-item bg-white rounded-xl shadow-sm border border-slate-200 overflow-hidden';
            
            const header = document.createElement('div');
            header.className = 'accordion-header flex justify-between items-center p-5 cursor-pointer hover:bg-slate-50';
            header.innerHTML = `
                <div class="flex items-center">
                    <span class="text-2xl mr-4">${item.icon}</span>
                    <h3 class="font-semibold text-lg text-slate-800">${item.title}</h3>
                </div>
                <svg class="accordion-icon w-6 h-6 text-slate-400 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path></svg>
            `;

            const content = document.createElement('div');
            content.className = 'accordion-content';
            const grid = document.createElement('div');
            grid.className = 'grid grid-cols-1 md:grid-cols-3 gap-x-8 gap-y-6';

            const engines = ['liquid', 'handlebars', 'sline'];
            const engineNames = { liquid: 'Liquid', handlebars: 'Handlebars', sline: 'Sline' };
            
            engines.forEach(engine => {
                if (filter === 'all' || filter === engine) {
                    const col = document.createElement('div');
                    col.innerHTML = `
                        <h4 class="font-bold text-lg mb-2 ${engineColors[engine].text}">${engineNames[engine]}</h4>
                        <div class="text-slate-600 text-sm leading-relaxed space-y-2">${item.content[engine]}</div>
                    `;
                    grid.appendChild(col);
                }
            });

            if (filter !== 'all') {
                grid.classList.remove('md:grid-cols-3');
                grid.classList.add('md:grid-cols-1');
            }
            
            content.appendChild(grid);
            accordionItem.appendChild(header);
            accordionItem.appendChild(content);
            accordionContainer.appendChild(accordionItem);

            header.addEventListener('click', () => {
                const isActive = accordionItem.classList.contains('active');
                document.querySelectorAll('.accordion-item').forEach(i => i.classList.remove('active'));
                if (!isActive) accordionItem.classList.add('active');
            });
        });
        const firstAccordion = document.querySelector('.accordion-item');
        if(firstAccordion) firstAccordion.classList.add('active');
    }

    function createRecommendations() {
        recommendationsContainer.innerHTML = '';
        DB.recommendations.forEach(item => {
            const colors = engineColors[item.engine];
            const card = document.createElement('div');
            card.className = `p-8 rounded-2xl border-2 ${colors.border} ${colors.bg} transition-all duration-300 hover:shadow-lg hover:border-transparent`;
            card.innerHTML = `
                <h4 class="text-xl font-bold mb-3 ${colors.text}">${item.title}</h4>
                <p class="text-sm text-slate-600 leading-relaxed">${item.description}</p>
                <div class="mt-6 text-xs font-bold uppercase tracking-wider ${colors.text}">推荐引擎: ${item.engine.charAt(0).toUpperCase() + item.engine.slice(1)}</div>
            `;
            recommendationsContainer.appendChild(card);
        });
    }

    function createRadarChart() {
        if (radarChart) {
            radarChart.destroy();
        }
        const ctx = document.getElementById('engineRadarChart').getContext('2d');
        const datasets = DB.radarData.datasets.map(ds => ({
            ...ds,
            pointBackgroundColor: ds.borderColor,
            pointBorderColor: '#fff',
            pointHoverBackgroundColor: '#fff',
            pointHoverBorderColor: ds.borderColor,
            borderWidth: 2.5,
            pointRadius: 4,
            pointHoverRadius: 6
        }));

        radarChart = new Chart(ctx, {
            type: 'radar',
            data: { labels: DB.radarData.labels, datasets: datasets },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { position: 'top', labels: { usePointStyle: true, boxWidth: 8, padding: 20, font: { size: 14 } } },
                    tooltip: {
                        callbacks: {
                            label: (context) => `${context.dataset.label}: ${context.raw}`
                        },
                        backgroundColor: '#0f172a',
                        titleFont: { size: 14, weight: 'bold' },
                        bodyFont: { size: 12 },
                        padding: 12,
                        cornerRadius: 8
                    }
                },
                scales: {
                    r: {
                        angleLines: { color: 'rgba(0, 0, 0, 0.08)' },
                        suggestedMin: 0,
                        suggestedMax: 5,
                        grid: { color: 'rgba(0, 0, 0, 0.08)' },
                        pointLabels: { font: { size: 14, weight: '500' }, color: '#334155' },
                        ticks: { display: false, stepSize: 1 }
                    }
                }
            }
        });
    }

    function updateView(activeTab) {
        const detailsContainer = document.getElementById('details-section');
        const recommendationsContainer = document.getElementById('recommendations-section');

        if (activeTab === 'compare') {
            compareSectionContainer.style.display = 'block';
            recommendationsContainer.style.display = 'block';
            detailsContainer.querySelector('h2').textContent = '九大维度详细分析';
            createAccordion('all');
        } else {
            compareSectionContainer.style.display = 'none';
            recommendationsContainer.style.display = 'none';
            const engineName = activeTab.charAt(0).toUpperCase() + activeTab.slice(1);
            detailsContainer.querySelector('h2').textContent = `${engineName} 引擎深度剖析`;
            createAccordion(activeTab);
        }
    }

    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            tabButtons.forEach(btn => btn.classList.remove('active'));
            button.classList.add('active');
            const activeTab = button.dataset.tab;
            updateView(activeTab);
        });
    });

    // Initial render
    createRadarChart();
    createAccordion();
    createRecommendations();
});
</script>

</body>
</html>