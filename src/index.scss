/* 全局重置和基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Spectral', 'Noto Serif SC', serif, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #2d3748;
    line-height: 1.6;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 导航栏样式 */
nav {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding: 1rem 0;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    transition: all 0.3s ease;
}

nav .nav-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.8rem;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.logo a {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    color: inherit;
    background: inherit;
    -webkit-background-clip: inherit;
    -webkit-text-fill-color: inherit;
    background-clip: inherit;
}

.logo-icon {
    width: 36px;
    height: 36px;
    object-fit: contain;
    position: relative;
    top: -9px;
    right: -32px;
}

.nav-links {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-links a {
    text-decoration: none;
    color: #4a5568;
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
}

.nav-links a:hover {
    color: #667eea;
}

.nav-links a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    transition: width 0.3s ease;
}

.nav-links a:hover::after {
    width: 100%;
}

/* 语言切换器样式 */
.language-switcher {
    position: relative;
    margin-left: 1rem;
}

.lang-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(103, 126, 234, 0.1);
    border: 1px solid rgba(103, 126, 234, 0.2);
    border-radius: 8px;
    padding: 0.5rem 1rem;
    color: #4a5568;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.lang-btn:hover {
    background: rgba(103, 126, 234, 0.15);
    border-color: rgba(103, 126, 234, 0.3);
    color: #667eea;
}

.current-lang {
    font-weight: 600;
}

.lang-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: 0.5rem;
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1000;
    min-width: 160px;
}

.lang-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.lang-option {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    width: 100%;
    padding: 0.75rem 1rem;
    border: none;
    background: transparent;
    color: #4a5568;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: left;
}

.lang-option:first-child {
    border-radius: 11px 11px 0 0;
}

.lang-option:last-child {
    border-radius: 0 0 11px 11px;
}

.lang-option:only-child {
    border-radius: 11px;
}

.lang-option:hover {
    background: #f7fafc;
    color: #667eea;
}

.lang-option.active {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.lang-option.active:hover {
    background: linear-gradient(135deg, #5a6fd8, #6a42a0);
}

.flag {
    font-size: 1.1rem;
}

/* 英雄区域样式 */
.hero {
    min-height: 80vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="tech-pattern" width="50" height="50" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><rect x="20" y="20" width="10" height="10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23tech-pattern)"/></svg>');
    opacity: 0.3;
    z-index: 1;
}

.hero-content {
    position: relative;
    z-index: 10; /* 确保内容在动画之上 */
    text-align: center;
    color: white;
    max-width: 800px;
    margin: 0 auto;
}

.hero h1 {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 800;
    margin-bottom: 1.5rem;
    text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    animation: fadeInUp 1s ease-out;
}

.hero-subtitle {
    font-size: 1.4rem;
    margin-bottom: 1.5rem;
    opacity: 0.9;
    animation: fadeInUp 1s ease-out 0.2s both;
    line-height: 1.4;
}

.hero-description {
    font-size: 1.2rem;
    margin-bottom: 2.5rem;
    opacity: 0.8;
    margin-left: auto;
    margin-right: auto;
    animation: fadeInUp 1s ease-out 0.4s both;
    line-height: 1.55;
}

.cta-buttons {
    display: flex;
    gap: 0.875rem;
    justify-content: center;
    flex-wrap: wrap;
    animation: fadeInUp 1s ease-out 0.6s both;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.875rem 1.75rem;
    border: none;
    border-radius: 50px;
    font-size: 1.05rem;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.btn-primary:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.btn-secondary {
    background: white;
    color: #667eea;
    border: 2px solid white;
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.9);
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

/* 主要内容区域 */
.main-content {
    background: white;
    position: relative;
    z-index: 10;
    margin-top: -1.5rem;
}

.section {
    padding: 6rem 0 2rem;
}

.section-title {
    font-size: 2.25rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 0.75rem;
    color: #2d3748;
    line-height: 1.2;
}
.stats .section-title {
    color: #FFF;
}
.section-subtitle {
    font-size: 1.1rem;
    text-align: center;
    color: #718096;
    margin-bottom: 2.5rem;
    max-width: 580px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.5;
}
.stats .section-subtitle {
    color: #FFF;
}
/* 特性网格 */
.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 0;
}

.feature-card {
    background: white;
    border-radius: 0.875rem;
    padding: 1.75rem;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.06);
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #667eea, #764ba2);
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
}

.feature-icon {
    font-size: 2.25rem;
    margin-bottom: 0.875rem;
    display: block;
}

.feature-card h3 {
    font-size: 1.375rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: #2d3748;
    line-height: 1.3;
}

.feature-card p {
    color: #718096;
    line-height: 1.55;
    font-size: 0.95rem;
}

/* 语法对比区域 */
.syntax-comparison {
    background: #f7fafc;
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
}

.comparison-header {
    display: grid;
    grid-template-columns: 1fr 1fr;
    background: #2d3748;
    color: white;
}

.comparison-title {
    padding: 1.25rem;
    text-align: center;
    font-weight: 600;
    font-size: 1.05rem;
    position: relative;
}

.comparison-title.liquid {
    background: #e53e3e;
}

.comparison-title.sline {
    background: #38a169;
}

.comparison-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 2px;
    background: rgba(255, 255, 255, 0.5);
}

.comparison-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
}

.code-panel {
    position: relative;
}

.code-panel.liquid {
    border-right: 1px solid #e2e8f0;
}

/* Shiki 3.x 样式优化 - 兼容新版本生成的 HTML */
.shiki, 
pre.shiki {
    margin: 0;
    padding: 1.25rem;
    font-size: 0.875rem;
    line-height: 1.55;
    overflow-x: auto;
    font-family: 'JetBrains Mono', 'Fira Code', 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    border-radius: 0; /* Shiki 3.x 会自动处理圆角 */
}

/* 确保 Shiki 3.x 生成的代码块样式正确 */
.shiki code {
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
}

/* 复制按钮样式 */
.copy-button {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(0, 0, 0, 0.1);
    color: #333;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
    z-index: 10;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.copy-button:hover {
    background: rgba(255, 255, 255, 1);
    border-color: rgba(0, 0, 0, 0.2);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.copy-button.copied {
    background: #28a745;
    border-color: #28a745;
    color: white;
}

/* 统计数据 */
.stats {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    text-align: center;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 1.5rem;
}

.stat-item h3 {
    font-size: 2.75rem;
    font-weight: 800;
    margin-bottom: 0.375rem;
    line-height: 1.1;
}

.stat-item p {
    font-size: 1.05rem;
    opacity: 0.9;
    line-height: 1.4;
}

/* CTA 区域 */
.cta-section {
    background: #f7fafc;
    text-align: center;
}

.cta-content {
    max-width: 600px;
    margin: 0 auto;
}

/* 页脚 */
footer {
    background: #2d3748;
    color: #e2e8f0;
    text-align: center;
    padding: 2.5rem 0;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.footer-section h4 {
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: white;
    font-size: 1.05rem;
}

.footer-section a {
    color: #a0aec0;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: #667eea;
}

.footer-bottom {
    border-top: 1px solid #4a5568;
    padding-top: 1.5rem;
    margin-top: 1.5rem;
    color: #a0aec0;
    font-size: 0.95rem;
}
.footer-bottom a {
    color: #fff;
}

/* 动画 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.8s ease-out;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .nav-links {
        display: none;
    }

    .hero h1 {
        font-size: 2.25rem;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
        gap: 0.75rem;
    }

    .comparison-header,
    .comparison-content {
        grid-template-columns: 1fr;
    }

    .code-panel.liquid {
        border-right: none;
        border-bottom: 1px solid #e2e8f0;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: 1.25rem;
    }

    .section {
        padding: 2.5rem 0;
    }

    .feature-card {
        padding: 1.5rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .section-subtitle {
        margin-bottom: 2rem;
        font-size: 1.05rem;
    }
}

/* 滚动指示器 */
.scroll-indicator {
    position: fixed;
    top: 0;
    left: 0;
    height: 4px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    z-index: 9999;
    transition: width 0.1s ease;
}